const { sequelize, SystemConfig, AdminUser, MessagePackage, SensitiveWord } = require('../config/database');
const logger = require('../utils/logger');

async function initDatabase() {
  try {
    console.log('开始初始化数据库...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步数据库模型（开发环境下强制重建表）
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ force: true });
      console.log('数据库模型同步完成（强制重建）');
    } else {
      await sequelize.sync({ alter: true });
      console.log('数据库模型同步完成');
    }

    // 初始化系统配置
    await SystemConfig.initDefaultConfigs();
    console.log('系统配置初始化完成');

    // 创建默认管理员
    const admin = await AdminUser.createDefaultAdmin();
    console.log('默认管理员创建完成:', admin.username);

    // 创建默认留言包
    await MessagePackage.createDefaultPackages();
    console.log('默认留言包创建完成');

    // 初始化默认敏感词
    await initDefaultSensitiveWords();
    console.log('默认敏感词初始化完成');

    console.log('数据库初始化完成！');
    console.log('默认管理员账号: admin');
    console.log('默认管理员密码: admin123');
    console.log('请及时修改默认密码！');

  } catch (error) {
    console.error('数据库初始化失败:', error);
    logger.error('数据库初始化失败', { error: error.message });
    process.exit(1);
  }
}

async function initDefaultSensitiveWords() {
  const defaultWords = [
    // 政治敏感词
    { word: '政治敏感词1', category: 'political', level: 'high', action: 'block' },
    { word: '政治敏感词2', category: 'political', level: 'high', action: 'block' },

    // 暴力相关
    { word: '暴力', category: 'violence', level: 'medium', action: 'replace', replacement: '***' },
    { word: '打架', category: 'violence', level: 'low', action: 'replace', replacement: '冲突' },

    // 色情相关
    { word: '色情', category: 'pornography', level: 'high', action: 'block' },

    // 赌博相关
    { word: '赌博', category: 'gambling', level: 'high', action: 'block' },
    { word: '彩票', category: 'gambling', level: 'medium', action: 'review' },

    // 毒品相关
    { word: '毒品', category: 'drugs', level: 'high', action: 'block' },

    // 其他
    { word: '垃圾信息', category: 'other', level: 'medium', action: 'replace', replacement: '不当信息' },
    { word: '广告', category: 'other', level: 'low', action: 'review' }
  ];

  await SensitiveWord.batchCreate(defaultWords);
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = { initDatabase };
