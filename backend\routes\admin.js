const express = require('express');
const router = express.Router();
const { AdminUser, User, Message, SensitiveWord, SystemConfig, PaymentOrder } = require('../config/database');
const { authenticateAdmin, generateToken, checkPermission } = require('../middleware/auth');
const { validateAdminLogin, validateSensitiveWord, validateSystemConfig } = require('../middleware/validation');
const ResponseHelper = require('../utils/response');
const logger = require('../utils/logger');

// 管理员登录
router.post('/login', validateAdminLogin, async (req, res) => {
  try {
    const { username, password } = req.body;

    // 查找管理员
    const admin = await AdminUser.findByUsername(username);
    if (!admin) {
      logger.logSecurity('admin_login_attempt_invalid_user', {
        username,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 检查账户状态
    if (admin.isLocked()) {
      logger.logSecurity('admin_login_attempt_locked', {
        adminId: admin.id,
        username,
        ip: req.ip
      });
      return ResponseHelper.forbidden(res, '账户已被锁定，请稍后再试');
    }

    if (admin.status !== 'active') {
      logger.logSecurity('admin_login_attempt_inactive', {
        adminId: admin.id,
        username,
        status: admin.status,
        ip: req.ip
      });
      return ResponseHelper.forbidden(res, '账户已被禁用');
    }

    // 验证密码
    const isValidPassword = await admin.validatePassword(password);
    if (!isValidPassword) {
      await admin.recordFailedLogin();
      logger.logSecurity('admin_login_attempt_invalid_password', {
        adminId: admin.id,
        username,
        ip: req.ip
      });
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 更新登录信息
    await admin.updateLoginInfo(req.ip);

    // 生成JWT令牌
    const token = generateToken({ id: admin.id, type: 'admin' });

    // 记录日志
    logger.logAdminAction(admin.id, 'login', {
      username,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    ResponseHelper.success(res, {
      admin: admin.toJSON(),
      token
    }, '登录成功');

  } catch (error) {
    logger.error('管理员登录失败', { error: error.message, body: req.body });
    ResponseHelper.serverError(res, '登录失败', error);
  }
});

// 获取仪表板统计数据
router.get('/dashboard', authenticateAdmin, async (req, res) => {
  try {
    // 用户统计
    const userStats = await User.findAndCountAll({
      attributes: ['status'],
      group: ['status']
    });

    // 留言统计
    const messageStats = await Message.findAndCountAll({
      attributes: ['status', 'isPublic'],
      group: ['status', 'isPublic']
    });

    // 今日新增统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayUsers = await User.count({
      where: {
        createdAt: {
          [require('sequelize').Op.gte]: today
        }
      }
    });

    const todayMessages = await Message.count({
      where: {
        createdAt: {
          [require('sequelize').Op.gte]: today
        }
      }
    });

    // 支付统计
    const paymentStats = await PaymentOrder.findAndCountAll({
      attributes: ['status'],
      group: ['status']
    });

    const stats = {
      users: {
        total: await User.count(),
        today: todayUsers,
        byStatus: userStats.rows
      },
      messages: {
        total: await Message.count(),
        today: todayMessages,
        byStatus: messageStats.rows
      },
      payments: {
        total: await PaymentOrder.count(),
        byStatus: paymentStats.rows
      },
      sensitiveWords: {
        total: await SensitiveWord.count(),
        active: await SensitiveWord.count({ where: { isActive: true } })
      }
    };

    ResponseHelper.success(res, stats, '获取仪表板数据成功');

  } catch (error) {
    logger.error('获取仪表板数据失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '获取仪表板数据失败', error);
  }
});

// 获取用户列表
router.get('/users', authenticateAdmin, checkPermission('user.view'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const result = await User.findAndCountAll({
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取用户列表成功');

  } catch (error) {
    logger.error('获取用户列表失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '获取用户列表失败', error);
  }
});

// 获取留言列表
router.get('/messages', authenticateAdmin, checkPermission('message.view'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const status = req.query.status;

    const where = {};
    if (status) {
      where.status = status;
    }

    const result = await Message.findAndCountAll({
      where,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'nickname']
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取留言列表成功');

  } catch (error) {
    logger.error('获取留言列表失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '获取留言列表失败', error);
  }
});

// 审核留言
router.put('/messages/:id/audit', authenticateAdmin, checkPermission('message.audit'), async (req, res) => {
  try {
    const messageId = req.params.id;
    const { status, rejectReason } = req.body;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return ResponseHelper.notFound(res, '留言不存在');
    }

    message.status = status;
    if (status === 'rejected' && rejectReason) {
      message.rejectReason = rejectReason;
    }
    await message.save();

    // 记录日志
    logger.logAdminAction(req.admin.id, 'audit_message', {
      messageId,
      status,
      rejectReason
    });

    ResponseHelper.success(res, message, '审核完成');

  } catch (error) {
    logger.error('审核留言失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '审核留言失败', error);
  }
});

// 获取敏感词列表
router.get('/sensitive-words', authenticateAdmin, checkPermission('sensitive_word.view'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    const result = await SensitiveWord.findAndCountAll({
      order: [['hitCount', 'DESC'], ['word', 'ASC']],
      limit,
      offset
    });

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取敏感词列表成功');

  } catch (error) {
    logger.error('获取敏感词列表失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '获取敏感词列表失败', error);
  }
});

// 添加敏感词
router.post('/sensitive-words', authenticateAdmin, checkPermission('sensitive_word.edit'), validateSensitiveWord, async (req, res) => {
  try {
    const { word, category, level, action, replacement } = req.body;

    const sensitiveWord = await SensitiveWord.create({
      word,
      category,
      level,
      action,
      replacement,
      createdBy: req.admin.id
    });

    // 记录日志
    logger.logAdminAction(req.admin.id, 'create_sensitive_word', { word, category });

    ResponseHelper.created(res, sensitiveWord, '添加敏感词成功');

  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      return ResponseHelper.conflict(res, '敏感词已存在');
    }
    logger.error('添加敏感词失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '添加敏感词失败', error);
  }
});

// 获取系统配置
router.get('/configs', authenticateAdmin, checkPermission('config.view'), async (req, res) => {
  try {
    const category = req.query.category;
    
    let configs;
    if (category) {
      configs = await SystemConfig.getByCategory(category);
    } else {
      configs = await SystemConfig.findAll({
        order: [['category', 'ASC'], ['sortOrder', 'ASC']]
      });
    }

    ResponseHelper.success(res, configs, '获取系统配置成功');

  } catch (error) {
    logger.error('获取系统配置失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '获取系统配置失败', error);
  }
});

// 更新系统配置
router.put('/configs/:key', authenticateAdmin, checkPermission('config.edit'), async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;

    const result = await SystemConfig.setValue(key, value, req.admin.id);
    if (!result) {
      return ResponseHelper.notFound(res, '配置项不存在');
    }

    // 记录日志
    logger.logAdminAction(req.admin.id, 'update_config', { key, value });

    ResponseHelper.success(res, result, '更新配置成功');

  } catch (error) {
    logger.error('更新系统配置失败', { error: error.message, adminId: req.admin?.id });
    ResponseHelper.serverError(res, '更新系统配置失败', error);
  }
});

module.exports = router;
