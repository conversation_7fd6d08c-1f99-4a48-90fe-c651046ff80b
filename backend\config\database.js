const { Sequelize } = require('sequelize');
require('dotenv').config();

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'message_wall',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || 'qq674482179',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    timezone: '+08:00', // 设置时区为中国标准时间
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      timestamps: true,
      underscored: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

// 导入模型
const User = require('../models/User');
const Message = require('../models/Message');
const MessageLike = require('../models/MessageLike');
const SensitiveWord = require('../models/SensitiveWord');
const WechatUser = require('../models/WechatUser');
const PaymentOrder = require('../models/PaymentOrder');
const MessagePackage = require('../models/MessagePackage');
const SystemConfig = require('../models/SystemConfig');
const AdminUser = require('../models/AdminUser');

// 定义模型关联
const defineAssociations = () => {
  // 用户和留言的关联
  User.hasMany(Message, { foreignKey: 'userId', as: 'messages' });
  Message.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 用户和留言点赞的关联
  User.hasMany(MessageLike, { foreignKey: 'userId', as: 'likes' });
  MessageLike.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 留言和点赞的关联
  Message.hasMany(MessageLike, { foreignKey: 'messageId', as: 'likes' });
  MessageLike.belongsTo(Message, { foreignKey: 'messageId', as: 'message' });

  // 用户和微信用户的关联
  User.hasOne(WechatUser, { foreignKey: 'userId', as: 'wechatUser' });
  WechatUser.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 用户和支付订单的关联
  User.hasMany(PaymentOrder, { foreignKey: 'userId', as: 'orders' });
  PaymentOrder.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 留言包和支付订单的关联
  MessagePackage.hasMany(PaymentOrder, { foreignKey: 'packageId', as: 'orders' });
  PaymentOrder.belongsTo(MessagePackage, { foreignKey: 'packageId', as: 'package' });
};

// 初始化模型
User.init(User.getAttributes(), User.getOptions(sequelize));
Message.init(Message.getAttributes(), Message.getOptions(sequelize));
MessageLike.init(MessageLike.getAttributes(), MessageLike.getOptions(sequelize));
SensitiveWord.init(SensitiveWord.getAttributes(), SensitiveWord.getOptions(sequelize));
WechatUser.init(WechatUser.getAttributes(), WechatUser.getOptions(sequelize));
PaymentOrder.init(PaymentOrder.getAttributes(), PaymentOrder.getOptions(sequelize));
MessagePackage.init(MessagePackage.getAttributes(), MessagePackage.getOptions(sequelize));
SystemConfig.init(SystemConfig.getAttributes(), SystemConfig.getOptions(sequelize));
AdminUser.init(AdminUser.getAttributes(), AdminUser.getOptions(sequelize));

// 定义关联
defineAssociations();

module.exports = {
  sequelize,
  User,
  Message,
  MessageLike,
  SensitiveWord,
  WechatUser,
  PaymentOrder,
  MessagePackage,
  SystemConfig,
  AdminUser
};
