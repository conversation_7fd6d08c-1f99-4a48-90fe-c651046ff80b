const { DataTypes, Model } = require('sequelize');

class WechatUser extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      openid: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '微信OpenID'
      },
      unionid: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '微信UnionID'
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: '关联的用户ID'
      },
      nickname: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '微信昵称'
      },
      headimgurl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '微信头像URL'
      },
      sex: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '性别：1男性，2女性，0未知'
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '国家'
      },
      province: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '省份'
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '城市'
      },
      language: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '语言'
      },
      accessToken: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '访问令牌'
      },
      refreshToken: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '刷新令牌'
      },
      expiresIn: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '令牌过期时间（秒）'
      },
      scope: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '授权作用域'
      },
      lastLogin: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '最后登录时间'
      },
      loginCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '登录次数'
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: '是否活跃'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'WechatUser',
      tableName: 'wechat_users',
      indexes: [
        {
          fields: ['openid']
        },
        {
          fields: ['unionid']
        },
        {
          fields: ['userId']
        }
      ]
    };
  }

  // 静态方法
  static async findByOpenid(openid) {
    return await this.findOne({
      where: { openid },
      include: [{
        model: require('./User'),
        as: 'user'
      }]
    });
  }

  static async createOrUpdate(wechatData) {
    const { openid } = wechatData;
    const [wechatUser, created] = await this.findOrCreate({
      where: { openid },
      defaults: wechatData
    });

    if (!created) {
      await wechatUser.update(wechatData);
    }

    return wechatUser;
  }

  static async bindUser(openid, userId) {
    const wechatUser = await this.findByOpenid(openid);
    if (wechatUser) {
      wechatUser.userId = userId;
      await wechatUser.save();
      return wechatUser;
    }
    return null;
  }

  // 实例方法
  async updateLoginInfo() {
    this.lastLogin = new Date();
    this.loginCount += 1;
    await this.save();
  }

  async updateTokens(accessToken, refreshToken, expiresIn) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.expiresIn = expiresIn;
    await this.save();
  }
}

module.exports = WechatUser;
