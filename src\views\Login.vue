<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 头部 -->
      <div class="login-header">
        <h1 class="login-title">欢迎回来</h1>
        <p class="login-subtitle">登录到留言墙</p>
      </div>

      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label class="form-label">用户名</label>
          <input
            v-model="form.username"
            type="text"
            class="input"
            placeholder="请输入用户名"
            required
            :disabled="loading"
          >
        </div>

        <div class="form-group">
          <label class="form-label">密码</label>
          <input
            v-model="form.password"
            type="password"
            class="input"
            placeholder="请输入密码"
            required
            :disabled="loading"
          >
        </div>

        <button
          type="submit"
          class="btn btn-primary w-full"
          :disabled="loading"
        >
          <LoadingSpinner v-if="loading" size="small" color="white" />
          <span v-else>登录</span>
        </button>
      </form>

      <!-- 微信登录 -->
      <div v-if="wechatConfigured" class="wechat-login">
        <div class="divider">
          <span>或</span>
        </div>
        
        <button
          @click="handleWechatLogin"
          class="btn btn-outline w-full"
          :disabled="loading"
        >
          <i class="iconfont icon-wechat mr-2 text-green-600"></i>
          微信登录
        </button>
      </div>

      <!-- 底部链接 -->
      <div class="login-footer">
        <p class="text-center text-gray-600">
          还没有账号？
          <router-link to="/register" class="text-green-600 hover:text-green-700">
            立即注册
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    // 响应式数据
    const form = ref({
      username: '',
      password: ''
    })
    
    // 计算属性
    const loading = computed(() => store.getters['auth/loginLoading'])
    const wechatConfigured = computed(() => store.getters['config/isWechatConfigured'])
    
    // 方法
    const handleLogin = async () => {
      try {
        await store.dispatch('auth/login', form.value)
        store.dispatch('toast/success', '登录成功')
        
        // 跳转到首页或之前的页面
        const redirect = router.currentRoute.value.query.redirect || '/'
        router.push(redirect)
      } catch (error) {
        store.dispatch('toast/error', error.message || '登录失败')
      }
    }
    
    const handleWechatLogin = () => {
      // 生成state参数用于二维码登录
      const state = Date.now().toString()
      const redirectUri = `${window.location.origin}/api/wechat/callback`
      
      // 跳转到微信授权页面
      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${store.getters['config/wechatConfig'].appId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
      
      window.location.href = authUrl
    }
    
    // 生命周期
    onMounted(async () => {
      // 获取微信配置
      try {
        await store.dispatch('config/fetchWechatConfig')
      } catch (error) {
        console.error('获取微信配置失败:', error)
      }
    })
    
    return {
      form,
      loading,
      wechatConfigured,
      handleLogin,
      handleWechatLogin
    }
  }
}
</script>

<style scoped>
.login-page {
  @apply min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4;
}

.login-container {
  @apply bg-white rounded-lg shadow-xl p-8 w-full max-w-md;
}

.login-header {
  @apply text-center mb-8;
}

.login-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.login-subtitle {
  @apply text-gray-600;
}

.login-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.wechat-login {
  @apply mt-6;
}

.divider {
  @apply relative text-center my-6;
}

.divider::before {
  @apply absolute inset-0 flex items-center;
  content: '';
}

.divider::before {
  @apply border-t border-gray-300;
}

.divider span {
  @apply bg-white px-3 text-gray-500 text-sm;
}

.login-footer {
  @apply mt-8;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .login-container {
    @apply p-6;
  }
}
</style>
