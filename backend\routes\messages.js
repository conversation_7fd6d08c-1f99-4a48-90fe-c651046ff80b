const express = require('express');
const router = express.Router();
const { Message, MessageLike, SensitiveWord, SystemConfig } = require('../config/database');
const { authenticateUser, optionalAuth } = require('../middleware/auth');
const { validateMessageCreation, validateMessageQuery, validatePagination, validateId } = require('../middleware/validation');
const ResponseHelper = require('../utils/response');
const logger = require('../utils/logger');

// 获取公开留言列表
router.get('/public', validatePagination, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const result = await Message.findPublicMessages(limit, offset);

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取公开留言成功');

  } catch (error) {
    logger.error('获取公开留言失败', { error: error.message, query: req.query });
    ResponseHelper.serverError(res, '获取公开留言失败', error);
  }
});

// 查询留言（按名字和暗号）
router.post('/search', validateMessageQuery, validatePagination, async (req, res) => {
  try {
    const { senderName, secretCode } = req.body;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const result = await Message.findByNameAndCode(senderName, secretCode, limit, offset);

    // 增加查看次数
    if (result.rows.length > 0) {
      for (const message of result.rows) {
        await message.incrementView();
      }
    }

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, 
      result.count > 0 ? '找到留言' : '没有找到留言');

  } catch (error) {
    logger.error('查询留言失败', { error: error.message, body: req.body });
    ResponseHelper.serverError(res, '查询留言失败', error);
  }
});

// 创建留言
router.post('/', authenticateUser, validateMessageCreation, async (req, res) => {
  try {
    const { senderName, secretCode, content, isPublic = false } = req.body;
    const userId = req.user.id;

    // 检查是否启用付费发布
    const paymentEnabled = await SystemConfig.getValue('payment_enabled', false);
    
    if (paymentEnabled) {
      // 检查用户留言余额
      if (req.user.messageBalance <= 0) {
        return ResponseHelper.error(res, '留言余额不足，请购买留言包', 402);
      }
    }

    // 敏感词检测
    const sensitiveWordsEnabled = await SystemConfig.getValue('sensitive_words_enabled', true);
    let filteredContent = content;
    let hasSensitiveWords = false;
    let foundWords = [];

    if (sensitiveWordsEnabled) {
      const checkResult = await SensitiveWord.checkContent(content);
      filteredContent = checkResult.filteredContent;
      hasSensitiveWords = checkResult.hasWords;
      foundWords = checkResult.foundWords;
    }

    // 创建留言
    const message = await Message.create({
      userId,
      senderName,
      secretCode,
      content: filteredContent,
      originalContent: content,
      isPublic,
      hasSensitiveWords,
      sensitiveWords: foundWords,
      status: hasSensitiveWords ? 'pending' : 'approved',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 如果启用付费发布，扣除用户余额
    if (paymentEnabled) {
      req.user.messageBalance -= 1;
      req.user.totalMessages += 1;
      await req.user.save();
    }

    // 记录日志
    logger.logUserAction(userId, 'create_message', {
      messageId: message.id,
      senderName,
      isPublic,
      hasSensitiveWords,
      ip: req.ip
    });

    ResponseHelper.created(res, message, '留言发布成功');

  } catch (error) {
    logger.error('创建留言失败', { error: error.message, body: req.body, userId: req.user?.id });
    ResponseHelper.serverError(res, '创建留言失败', error);
  }
});

// 获取用户的留言列表
router.get('/my', authenticateUser, validatePagination, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.user.id;

    const result = await Message.findUserMessages(userId, limit, offset);

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取我的留言成功');

  } catch (error) {
    logger.error('获取用户留言失败', { error: error.message, userId: req.user?.id });
    ResponseHelper.serverError(res, '获取我的留言失败', error);
  }
});

// 获取留言详情
router.get('/:id', validateId, optionalAuth, async (req, res) => {
  try {
    const messageId = req.params.id;
    
    const message = await Message.findByPk(messageId, {
      include: [{
        model: require('../config/database').User,
        as: 'user',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }]
    });

    if (!message) {
      return ResponseHelper.notFound(res, '留言不存在');
    }

    // 检查访问权限
    if (!message.isPublic && (!req.user || req.user.id !== message.userId)) {
      return ResponseHelper.forbidden(res, '无权访问此留言');
    }

    // 增加查看次数
    await message.incrementView();

    ResponseHelper.success(res, message, '获取留言详情成功');

  } catch (error) {
    logger.error('获取留言详情失败', { error: error.message, messageId: req.params.id });
    ResponseHelper.serverError(res, '获取留言详情失败', error);
  }
});

// 点赞/取消点赞留言
router.post('/:id/like', authenticateUser, validateId, async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return ResponseHelper.notFound(res, '留言不存在');
    }

    // 检查是否已点赞
    const existingLike = await MessageLike.findUserLike(userId, messageId);

    if (existingLike) {
      // 取消点赞
      await MessageLike.removeLike(userId, messageId);
      await message.decrementLike();
      
      logger.logUserAction(userId, 'unlike_message', { messageId });
      ResponseHelper.success(res, { liked: false, likeCount: message.likeCount }, '取消点赞成功');
    } else {
      // 点赞
      await MessageLike.createLike(userId, messageId);
      await message.incrementLike();
      
      logger.logUserAction(userId, 'like_message', { messageId });
      ResponseHelper.success(res, { liked: true, likeCount: message.likeCount }, '点赞成功');
    }

  } catch (error) {
    logger.error('点赞操作失败', { error: error.message, messageId: req.params.id, userId: req.user?.id });
    ResponseHelper.serverError(res, '点赞操作失败', error);
  }
});

// 删除留言
router.delete('/:id', authenticateUser, validateId, async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return ResponseHelper.notFound(res, '留言不存在');
    }

    // 检查权限
    if (message.userId !== userId) {
      return ResponseHelper.forbidden(res, '只能删除自己的留言');
    }

    // 删除相关的点赞记录
    await MessageLike.destroy({ where: { messageId } });
    
    // 删除留言
    await message.destroy();

    // 记录日志
    logger.logUserAction(userId, 'delete_message', { messageId });

    ResponseHelper.success(res, null, '删除留言成功');

  } catch (error) {
    logger.error('删除留言失败', { error: error.message, messageId: req.params.id, userId: req.user?.id });
    ResponseHelper.serverError(res, '删除留言失败', error);
  }
});

module.exports = router;
