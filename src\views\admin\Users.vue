<template>
  <div class="users-page">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">用户管理</h1>
    
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-4">
          <input
            type="text"
            placeholder="搜索用户..."
            class="input w-64"
          >
          <select class="input w-32">
            <option value="">全部状态</option>
            <option value="active">正常</option>
            <option value="inactive">禁用</option>
            <option value="banned">封禁</option>
          </select>
        </div>
        <button class="btn btn-primary">
          <i class="iconfont icon-plus mr-2"></i>
          添加用户
        </button>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b">
              <th class="text-left py-3 px-4">用户名</th>
              <th class="text-left py-3 px-4">昵称</th>
              <th class="text-left py-3 px-4">邮箱</th>
              <th class="text-left py-3 px-4">留言数</th>
              <th class="text-left py-3 px-4">状态</th>
              <th class="text-left py-3 px-4">注册时间</th>
              <th class="text-left py-3 px-4">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b hover:bg-gray-50">
              <td class="py-3 px-4">user001</td>
              <td class="py-3 px-4">张三</td>
              <td class="py-3 px-4"><EMAIL></td>
              <td class="py-3 px-4">15</td>
              <td class="py-3 px-4">
                <span class="badge badge-success">正常</span>
              </td>
              <td class="py-3 px-4">2024-01-01</td>
              <td class="py-3 px-4">
                <button class="btn btn-outline btn-sm mr-2">编辑</button>
                <button class="btn btn-danger btn-sm">禁用</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="flex items-center justify-between mt-4">
        <div class="text-sm text-gray-600">
          显示 1-10 条，共 100 条记录
        </div>
        <div class="flex space-x-2">
          <button class="btn btn-outline btn-sm">上一页</button>
          <button class="btn btn-outline btn-sm">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminUsers'
}
</script>

<style scoped>
.btn-sm {
  @apply px-3 py-1 text-sm;
}

table th {
  @apply font-semibold text-gray-700;
}
</style>
