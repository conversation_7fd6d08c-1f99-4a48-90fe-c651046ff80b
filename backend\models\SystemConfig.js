const { DataTypes, Model } = require('sequelize');

class SystemConfig extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      key: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '配置键'
      },
      value: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '配置值'
      },
      type: {
        type: DataTypes.ENUM('string', 'number', 'boolean', 'json'),
        defaultValue: 'string',
        comment: '值类型'
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '配置分类'
      },
      description: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '配置描述'
      },
      isPublic: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否公开（前端可访问）'
      },
      isRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否必需'
      },
      defaultValue: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '默认值'
      },
      validation: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '验证规则'
      },
      sortOrder: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '排序'
      },
      updatedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '更新者ID'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'SystemConfig',
      tableName: 'system_configs',
      indexes: [
        {
          fields: ['key']
        },
        {
          fields: ['category']
        },
        {
          fields: ['isPublic']
        }
      ]
    };
  }

  // 静态方法
  static async getValue(key, defaultValue = null) {
    const config = await this.findOne({ where: { key } });
    if (!config) return defaultValue;

    return this.parseValue(config.value, config.type);
  }

  static async setValue(key, value, updatedBy = null) {
    const config = await this.findOne({ where: { key } });
    if (config) {
      config.value = this.stringifyValue(value, config.type);
      config.updatedBy = updatedBy;
      await config.save();
      return config;
    }
    return null;
  }

  static async getByCategory(category, isPublic = null) {
    const where = { category };
    if (isPublic !== null) {
      where.isPublic = isPublic;
    }

    const configs = await this.findAll({
      where,
      order: [['sortOrder', 'ASC'], ['key', 'ASC']]
    });

    const result = {};
    configs.forEach(config => {
      result[config.key] = this.parseValue(config.value, config.type);
    });

    return result;
  }

  static async getPublicConfigs() {
    const configs = await this.findAll({
      where: { isPublic: true },
      order: [['category', 'ASC'], ['sortOrder', 'ASC']]
    });

    const result = {};
    configs.forEach(config => {
      if (!result[config.category]) {
        result[config.category] = {};
      }
      result[config.category][config.key] = this.parseValue(config.value, config.type);
    });

    return result;
  }

  static parseValue(value, type) {
    if (value === null || value === undefined) return null;

    switch (type) {
      case 'number':
        return Number(value);
      case 'boolean':
        return value === 'true' || value === true;
      case 'json':
        try {
          return JSON.parse(value);
        } catch (e) {
          return null;
        }
      default:
        return value;
    }
  }

  static stringifyValue(value, type) {
    if (value === null || value === undefined) return null;

    switch (type) {
      case 'json':
        return JSON.stringify(value);
      case 'boolean':
        return value ? 'true' : 'false';
      default:
        return String(value);
    }
  }

  static async initDefaultConfigs() {
    const defaultConfigs = [
      // 微信配置
      {
        key: 'wechat_app_id',
        value: '',
        type: 'string',
        category: 'wechat',
        description: '微信公众号AppID',
        isRequired: true,
        sortOrder: 1
      },
      {
        key: 'wechat_app_secret',
        value: '',
        type: 'string',
        category: 'wechat',
        description: '微信公众号AppSecret',
        isRequired: true,
        sortOrder: 2
      },
      // 支付配置
      {
        key: 'payment_enabled',
        value: 'false',
        type: 'boolean',
        category: 'payment',
        description: '是否启用付费发布',
        isPublic: true,
        defaultValue: 'false',
        sortOrder: 1
      },
      {
        key: 'wechat_mch_id',
        value: '',
        type: 'string',
        category: 'payment',
        description: '微信商户号',
        sortOrder: 2
      },
      {
        key: 'wechat_api_key',
        value: '',
        type: 'string',
        category: 'payment',
        description: '微信支付密钥',
        sortOrder: 3
      },
      // 系统配置
      {
        key: 'site_name',
        value: '留言墙',
        type: 'string',
        category: 'system',
        description: '网站名称',
        isPublic: true,
        defaultValue: '留言墙',
        sortOrder: 1
      },
      {
        key: 'max_message_length',
        value: '500',
        type: 'number',
        category: 'system',
        description: '留言最大长度',
        isPublic: true,
        defaultValue: '500',
        sortOrder: 2
      },
      {
        key: 'sensitive_words_enabled',
        value: 'true',
        type: 'boolean',
        category: 'system',
        description: '是否启用敏感词过滤',
        defaultValue: 'true',
        sortOrder: 3
      }
    ];

    for (const config of defaultConfigs) {
      await this.findOrCreate({
        where: { key: config.key },
        defaults: config
      });
    }
  }
}

module.exports = SystemConfig;
