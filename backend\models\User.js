const { DataTypes, Model } = require('sequelize');
const bcrypt = require('bcryptjs');

class User extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '用户名'
      },
      password: {
        type: DataTypes.STRING(255),
        allowNull: true, // 微信登录用户可以没有密码
        comment: '密码（加密后）'
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
        validate: {
          isEmail: true
        },
        comment: '邮箱'
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '手机号'
      },
      nickname: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '昵称'
      },
      avatar: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '头像URL'
      },
      messageBalance: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '留言余额'
      },
      totalMessages: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '总留言数'
      },
      isAdmin: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否为管理员'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'banned'),
        defaultValue: 'active',
        comment: '用户状态'
      },
      lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '最后登录时间'
      },
      lastLoginIp: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: '最后登录IP'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'User',
      tableName: 'users',
      hooks: {
        beforeCreate: async (user) => {
          if (user.password) {
            user.password = await bcrypt.hash(user.password, 10);
          }
        },
        beforeUpdate: async (user) => {
          if (user.changed('password') && user.password) {
            user.password = await bcrypt.hash(user.password, 10);
          }
        }
      },
      instanceMethods: {
        validatePassword: async function(password) {
          if (!this.password) return false;
          return await bcrypt.compare(password, this.password);
        },
        toJSON: function() {
          const values = { ...this.get() };
          delete values.password;
          return values;
        }
      }
    };
  }

  // 实例方法
  async validatePassword(password) {
    if (!this.password) return false;
    return await bcrypt.compare(password, this.password);
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }

  // 静态方法
  static async findByUsername(username) {
    return await this.findOne({ where: { username } });
  }

  static async findByEmail(email) {
    return await this.findOne({ where: { email } });
  }

  static async createUser(userData) {
    return await this.create(userData);
  }
}

module.exports = User;
