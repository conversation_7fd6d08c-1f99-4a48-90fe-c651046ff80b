<template>
  <div class="register-page">
    <div class="register-container">
      <!-- 头部 -->
      <div class="register-header">
        <h1 class="register-title">创建账号</h1>
        <p class="register-subtitle">加入留言墙大家庭</p>
      </div>

      <!-- 注册表单 -->
      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label class="form-label">用户名 *</label>
          <input
            v-model="form.username"
            type="text"
            class="input"
            :class="{ 'border-red-500': errors.username }"
            placeholder="3-50个字符，只能包含字母、数字和下划线"
            required
            :disabled="loading"
            @blur="validateUsername"
          >
          <p v-if="errors.username" class="error-text">{{ errors.username }}</p>
        </div>

        <div class="form-group">
          <label class="form-label">密码 *</label>
          <input
            v-model="form.password"
            type="password"
            class="input"
            :class="{ 'border-red-500': errors.password }"
            placeholder="至少6个字符"
            required
            :disabled="loading"
            @blur="validatePassword"
          >
          <p v-if="errors.password" class="error-text">{{ errors.password }}</p>
        </div>

        <div class="form-group">
          <label class="form-label">确认密码 *</label>
          <input
            v-model="form.confirmPassword"
            type="password"
            class="input"
            :class="{ 'border-red-500': errors.confirmPassword }"
            placeholder="请再次输入密码"
            required
            :disabled="loading"
            @blur="validateConfirmPassword"
          >
          <p v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</p>
        </div>

        <div class="form-group">
          <label class="form-label">昵称</label>
          <input
            v-model="form.nickname"
            type="text"
            class="input"
            placeholder="显示名称（可选）"
            :disabled="loading"
          >
        </div>

        <div class="form-group">
          <label class="form-label">邮箱</label>
          <input
            v-model="form.email"
            type="email"
            class="input"
            :class="{ 'border-red-500': errors.email }"
            placeholder="邮箱地址（可选）"
            :disabled="loading"
            @blur="validateEmail"
          >
          <p v-if="errors.email" class="error-text">{{ errors.email }}</p>
        </div>

        <button
          type="submit"
          class="btn btn-primary w-full"
          :disabled="loading || !isFormValid"
        >
          <LoadingSpinner v-if="loading" size="small" color="white" />
          <span v-else>注册</span>
        </button>
      </form>

      <!-- 底部链接 -->
      <div class="register-footer">
        <p class="text-center text-gray-600">
          已有账号？
          <router-link to="/login" class="text-green-600 hover:text-green-700">
            立即登录
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { validateUsername, validateEmail, validatePassword } from '../utils/auth'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    // 响应式数据
    const form = ref({
      username: '',
      password: '',
      confirmPassword: '',
      nickname: '',
      email: ''
    })
    
    const errors = ref({})
    
    // 计算属性
    const loading = computed(() => store.getters['auth/loginLoading'])
    const isFormValid = computed(() => {
      return form.value.username && 
             form.value.password && 
             form.value.confirmPassword &&
             form.value.password === form.value.confirmPassword &&
             Object.keys(errors.value).length === 0
    })
    
    // 验证方法
    const validateUsernameField = () => {
      const result = validateUsername(form.value.username)
      if (!result.valid) {
        errors.value.username = result.message
      } else {
        delete errors.value.username
      }
    }
    
    const validatePasswordField = () => {
      const result = validatePassword(form.value.password)
      if (result.strength === 'weak') {
        errors.value.password = result.message
      } else {
        delete errors.value.password
      }
    }
    
    const validateConfirmPasswordField = () => {
      if (form.value.password !== form.value.confirmPassword) {
        errors.value.confirmPassword = '两次输入的密码不一致'
      } else {
        delete errors.value.confirmPassword
      }
    }
    
    const validateEmailField = () => {
      if (form.value.email) {
        const result = validateEmail(form.value.email)
        if (!result.valid) {
          errors.value.email = result.message
        } else {
          delete errors.value.email
        }
      } else {
        delete errors.value.email
      }
    }
    
    // 处理注册
    const handleRegister = async () => {
      // 验证所有字段
      validateUsernameField()
      validatePasswordField()
      validateConfirmPasswordField()
      validateEmailField()
      
      if (!isFormValid.value) {
        store.dispatch('toast/error', '请检查输入信息')
        return
      }
      
      try {
        const registerData = {
          username: form.value.username,
          password: form.value.password,
          nickname: form.value.nickname || form.value.username,
          email: form.value.email || undefined
        }
        
        await store.dispatch('auth/register', registerData)
        store.dispatch('toast/success', '注册成功')
        
        // 跳转到首页
        router.push('/')
      } catch (error) {
        store.dispatch('toast/error', error.message || '注册失败')
      }
    }
    
    return {
      form,
      errors,
      loading,
      isFormValid,
      handleRegister,
      validateUsername: validateUsernameField,
      validatePassword: validatePasswordField,
      validateConfirmPassword: validateConfirmPasswordField,
      validateEmail: validateEmailField
    }
  }
}
</script>

<style scoped>
.register-page {
  @apply min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4;
}

.register-container {
  @apply bg-white rounded-lg shadow-xl p-8 w-full max-w-md;
}

.register-header {
  @apply text-center mb-8;
}

.register-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.register-subtitle {
  @apply text-gray-600;
}

.register-form {
  @apply space-y-4;
}

.form-group {
  @apply space-y-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.error-text {
  @apply text-xs text-red-600;
}

.register-footer {
  @apply mt-8;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .register-container {
    @apply p-6;
  }
  
  .register-form {
    @apply space-y-3;
  }
}
</style>
