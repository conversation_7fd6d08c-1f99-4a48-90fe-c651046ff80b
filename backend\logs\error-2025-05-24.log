{"timestamp":"2025-05-24T02:08:53.263Z","level":"ERROR","message":"数据库初始化失败","error":"Cannot add or update a child row: a foreign key constraint fails (`message_wall`.`#sql-1460_9f`, CONSTRAINT `messages_userId_foreign_idx` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)"}
{"timestamp":"2025-05-24T02:09:24.512Z","level":"ERROR","message":"数据库初始化失败","error":"Cannot drop table 'messages' referenced by a foreign key constraint 'likes_ibfk_2' on table 'likes'."}
