const { DataTypes, Model } = require('sequelize');

class Message<PERSON>ike extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: '用户ID'
      },
      messageId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'messages',
          key: 'id'
        },
        comment: '留言ID'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'MessageLike',
      tableName: 'message_likes',
      indexes: [
        {
          unique: true,
          fields: ['userId', 'messageId']
        },
        {
          fields: ['messageId']
        }
      ]
    };
  }

  // 静态方法
  static async findUserLike(userId, messageId) {
    return await this.findOne({
      where: { userId, messageId }
    });
  }

  static async createLike(userId, messageId) {
    return await this.create({ userId, messageId });
  }

  static async removeLike(userId, messageId) {
    return await this.destroy({
      where: { userId, messageId }
    });
  }

  static async getUserLikedMessages(userId, limit = 10, offset = 0) {
    return await this.findAndCountAll({
      where: { userId },
      include: [{
        model: require('./Message'),
        as: 'message',
        include: [{
          model: require('./User'),
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'avatar']
        }]
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }
}

module.exports = MessageLike;
