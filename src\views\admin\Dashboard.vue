<template>
  <div class="dashboard">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">仪表板</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="stat-card">
        <div class="stat-icon bg-blue-100 text-blue-600">
          <i class="iconfont icon-user"></i>
        </div>
        <div>
          <div class="stat-number">1,234</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-green-100 text-green-600">
          <i class="iconfont icon-message"></i>
        </div>
        <div>
          <div class="stat-number">5,678</div>
          <div class="stat-label">总留言数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-yellow-100 text-yellow-600">
          <i class="iconfont icon-eye"></i>
        </div>
        <div>
          <div class="stat-number">12,345</div>
          <div class="stat-label">总浏览量</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-red-100 text-red-600">
          <i class="iconfont icon-heart"></i>
        </div>
        <div>
          <div class="stat-number">3,456</div>
          <div class="stat-label">总点赞数</div>
        </div>
      </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">最新留言</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between py-2 border-b">
            <div>
              <div class="font-medium">张三</div>
              <div class="text-sm text-gray-600">今天是个好日子...</div>
            </div>
            <div class="text-xs text-gray-500">2分钟前</div>
          </div>
          <div class="flex items-center justify-between py-2 border-b">
            <div>
              <div class="font-medium">李四</div>
              <div class="text-sm text-gray-600">感谢大家的支持...</div>
            </div>
            <div class="text-xs text-gray-500">5分钟前</div>
          </div>
          <div class="flex items-center justify-between py-2">
            <div>
              <div class="font-medium">王五</div>
              <div class="text-sm text-gray-600">祝愿世界和平...</div>
            </div>
            <div class="text-xs text-gray-500">10分钟前</div>
          </div>
        </div>
      </div>
      
      <div class="card">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">系统状态</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-gray-600">服务器状态</span>
            <span class="badge badge-success">正常</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">数据库状态</span>
            <span class="badge badge-success">正常</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">敏感词过滤</span>
            <span class="badge badge-success">启用</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">支付功能</span>
            <span class="badge badge-warning">未配置</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminDashboard'
}
</script>

<style scoped>
.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mr-4 text-xl;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600;
}
</style>
