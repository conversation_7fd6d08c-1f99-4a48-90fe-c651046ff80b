<template>
  <div class="admin-login-page">
    <div class="login-container">
      <div class="login-header">
        <h1 class="login-title">管理员登录</h1>
        <p class="login-subtitle">留言墙管理后台</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label class="form-label">用户名</label>
          <input
            v-model="form.username"
            type="text"
            class="input"
            placeholder="请输入管理员用户名"
            required
            :disabled="loading"
          >
        </div>

        <div class="form-group">
          <label class="form-label">密码</label>
          <input
            v-model="form.password"
            type="password"
            class="input"
            placeholder="请输入密码"
            required
            :disabled="loading"
          >
        </div>

        <button
          type="submit"
          class="btn btn-primary w-full"
          :disabled="loading"
        >
          <LoadingSpinner v-if="loading" size="small" color="white" />
          <span v-else>登录</span>
        </button>
      </form>

      <div class="login-footer">
        <p class="text-center text-gray-600">
          <router-link to="/" class="text-green-600 hover:text-green-700">
            ← 返回前台
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'AdminLogin',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const form = ref({
      username: '',
      password: ''
    })
    
    const loading = ref(false)
    
    const handleLogin = async () => {
      loading.value = true
      try {
        // 这里应该调用管理员登录API
        // 暂时使用模拟登录
        if (form.value.username === 'admin' && form.value.password === 'admin123') {
          store.dispatch('toast/success', '登录成功')
          router.push('/admin')
        } else {
          store.dispatch('toast/error', '用户名或密码错误')
        }
      } catch (error) {
        store.dispatch('toast/error', error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      handleLogin
    }
  }
}
</script>

<style scoped>
.admin-login-page {
  @apply min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50 flex items-center justify-center p-4;
}

.login-container {
  @apply bg-white rounded-lg shadow-xl p-8 w-full max-w-md;
}

.login-header {
  @apply text-center mb-8;
}

.login-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.login-subtitle {
  @apply text-gray-600;
}

.login-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.login-footer {
  @apply mt-8;
}
</style>
