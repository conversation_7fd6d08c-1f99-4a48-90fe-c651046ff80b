<template>
  <div class="search-page">
    <div class="container mx-auto px-4 py-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">找留言</h1>
      
      <!-- 搜索表单 -->
      <div class="card mb-6">
        <form @submit.prevent="handleSearch" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="form-label">留言者名字</label>
              <input
                v-model="searchForm.senderName"
                type="text"
                class="input"
                placeholder="请输入留言者名字"
                required
              >
            </div>
            <div>
              <label class="form-label">暗号</label>
              <input
                v-model="searchForm.secretCode"
                type="text"
                class="input"
                placeholder="请输入暗号"
                required
              >
            </div>
          </div>
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="searching"
          >
            <LoadingSpinner v-if="searching" size="small" color="white" />
            <span v-else>搜索留言</span>
          </button>
        </form>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-900">搜索结果</h2>
        <div
          v-for="message in searchResults"
          :key="message.id"
          class="message-card cursor-pointer"
          @click="viewMessage(message.id)"
        >
          <div class="flex items-start">
            <div class="avatar mr-3">
              {{ message.senderName.charAt(0) }}
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-gray-900">{{ message.senderName }}</h4>
                <span class="text-xs text-gray-500">{{ formatTime(message.createdAt) }}</span>
              </div>
              <p class="text-gray-700 text-sm">{{ message.content }}</p>
              <div class="flex items-center mt-2 text-xs text-gray-500">
                <span class="mr-4">
                  <i class="iconfont icon-eye mr-1"></i>
                  {{ message.viewCount }}
                </span>
                <span>
                  <i class="iconfont icon-heart mr-1"></i>
                  {{ message.likeCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="hasSearched && !searching" class="text-center py-12">
        <i class="iconfont icon-search text-4xl text-gray-400 mb-4"></i>
        <p class="text-gray-500">没有找到匹配的留言</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { formatDistanceToNow } from '../utils/date'

export default {
  name: 'Search',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    
    const searchForm = ref({
      senderName: '',
      secretCode: ''
    })
    
    const searching = ref(false)
    const searchResults = ref([])
    const hasSearched = ref(false)
    
    const handleSearch = async () => {
      if (!searchForm.value.senderName || !searchForm.value.secretCode) {
        store.dispatch('toast/warning', '请输入留言者名字和暗号')
        return
      }
      
      searching.value = true
      hasSearched.value = true
      
      try {
        const result = await store.dispatch('messages/searchMessages', searchForm.value)
        searchResults.value = result.messages
        
        if (result.messages.length === 0) {
          store.dispatch('toast/info', '没有找到匹配的留言')
        }
      } catch (error) {
        store.dispatch('toast/error', error.message || '搜索失败')
      } finally {
        searching.value = false
      }
    }
    
    const viewMessage = (id) => {
      router.push(`/message/${id}`)
    }
    
    const formatTime = (time) => {
      return formatDistanceToNow(new Date(time))
    }
    
    onMounted(() => {
      // 如果URL中有查询参数，自动填充并搜索
      if (route.query.senderName && route.query.secretCode) {
        searchForm.value.senderName = route.query.senderName
        searchForm.value.secretCode = route.query.secretCode
        handleSearch()
      }
    })
    
    return {
      searchForm,
      searching,
      searchResults,
      hasSearched,
      handleSearch,
      viewMessage,
      formatTime
    }
  }
}
</script>

<style scoped>
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
</style>
