<template>
  <div class="profile-page">
    <div class="container mx-auto px-4 py-6">
      <!-- 用户信息卡片 -->
      <div class="card mb-6">
        <div class="flex items-center">
          <div class="avatar-large mr-4">
            {{ user?.nickname?.charAt(0) || user?.username?.charAt(0) || 'U' }}
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-semibold text-gray-900">{{ user?.nickname || user?.username }}</h2>
            <p class="text-gray-600">{{ user?.email || '未设置邮箱' }}</p>
            <p class="text-sm text-gray-500">加入时间：{{ formatDate(user?.createdAt) }}</p>
          </div>
          <button @click="showEditProfile" class="btn btn-outline btn-sm">
            编辑资料
          </button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="card text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.messageBalance || 0 }}</div>
          <div class="text-sm text-gray-600">留言余额</div>
        </div>
        <div class="card text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.totalMessages || 0 }}</div>
          <div class="text-sm text-gray-600">总留言数</div>
        </div>
        <div class="card text-center">
          <div class="text-2xl font-bold text-purple-600">{{ stats.publicMessages || 0 }}</div>
          <div class="text-sm text-gray-600">公开留言</div>
        </div>
        <div class="card text-center">
          <div class="text-2xl font-bold text-red-600">{{ stats.totalLikes || 0 }}</div>
          <div class="text-sm text-gray-600">获得点赞</div>
        </div>
      </div>
      
      <!-- 功能菜单 -->
      <div class="space-y-4">
        <div class="card">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">我的功能</h3>
          <div class="space-y-3">
            <router-link to="/profile/messages" class="menu-item">
              <i class="iconfont icon-message text-green-600"></i>
              <span>我的留言</span>
              <i class="iconfont icon-arrow-right text-gray-400"></i>
            </router-link>
            
            <router-link to="/profile/likes" class="menu-item">
              <i class="iconfont icon-heart text-red-600"></i>
              <span>我的点赞</span>
              <i class="iconfont icon-arrow-right text-gray-400"></i>
            </router-link>
            
            <router-link v-if="isPaymentEnabled" to="/profile/orders" class="menu-item">
              <i class="iconfont icon-order text-blue-600"></i>
              <span>我的订单</span>
              <i class="iconfont icon-arrow-right text-gray-400"></i>
            </router-link>
            
            <button @click="showChangePassword" class="menu-item w-full">
              <i class="iconfont icon-lock text-yellow-600"></i>
              <span>修改密码</span>
              <i class="iconfont icon-arrow-right text-gray-400"></i>
            </button>
          </div>
        </div>
        
        <!-- 管理员入口 -->
        <div v-if="user?.isAdmin" class="card">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">管理功能</h3>
          <router-link to="/admin" class="menu-item">
            <i class="iconfont icon-admin text-purple-600"></i>
            <span>管理后台</span>
            <i class="iconfont icon-arrow-right text-gray-400"></i>
          </router-link>
        </div>
        
        <!-- 退出登录 -->
        <div class="card">
          <button @click="handleLogout" class="menu-item w-full text-red-600">
            <i class="iconfont icon-logout"></i>
            <span>退出登录</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { formatDate } from '../utils/date'

export default {
  name: 'Profile',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const stats = ref({})
    
    // 计算属性
    const user = computed(() => store.getters['auth/user'])
    const isPaymentEnabled = computed(() => store.getters['config/isPaymentEnabled'])
    
    // 方法
    const fetchStats = async () => {
      try {
        const response = await store.dispatch('users/fetchStats')
        stats.value = response
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }
    
    const showEditProfile = () => {
      store.dispatch('ui/showModal', {
        type: 'editProfile',
        user: user.value
      })
    }
    
    const showChangePassword = () => {
      store.dispatch('ui/showModal', {
        type: 'changePassword'
      })
    }
    
    const handleLogout = () => {
      store.dispatch('auth/logout')
      store.dispatch('toast/success', '已退出登录')
      router.push('/login')
    }
    
    // 生命周期
    onMounted(() => {
      fetchStats()
    })
    
    return {
      user,
      stats,
      isPaymentEnabled,
      showEditProfile,
      showChangePassword,
      handleLogout,
      formatDate
    }
  }
}
</script>

<style scoped>
.avatar-large {
  @apply w-16 h-16 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center text-white font-bold text-xl;
}

.menu-item {
  @apply flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors text-left;
}

.menu-item i:first-child {
  @apply text-xl mr-3;
}

.btn-sm {
  @apply px-3 py-1 text-sm;
}
</style>
