const mysql = require('mysql2/promise');
require('dotenv').config();

async function cleanDatabase() {
  let connection;
  
  try {
    console.log('开始清理数据库...');

    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'qq674482179',
      database: process.env.DB_NAME || 'message_wall'
    });

    console.log('数据库连接成功');

    // 禁用外键检查
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    console.log('已禁用外键检查');

    // 获取所有表名
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_TYPE = 'BASE TABLE'
    `, [process.env.DB_NAME || 'message_wall']);

    // 删除所有表
    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      await connection.execute(`DROP TABLE IF EXISTS \`${tableName}\``);
      console.log(`已删除表: ${tableName}`);
    }

    // 重新启用外键检查
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('已重新启用外键检查');

    console.log('数据库清理完成！');

  } catch (error) {
    console.error('数据库清理失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanDatabase().then(() => {
    console.log('可以运行 node scripts/init-database.js 来重新初始化数据库');
    process.exit(0);
  }).catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = { cleanDatabase };
