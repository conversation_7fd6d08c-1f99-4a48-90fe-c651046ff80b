const express = require('express');
const router = express.Router();
const QRCode = require('qrcode');
const NodeCache = require('node-cache');
const { WechatUser, User, SystemConfig } = require('../config/database');
const { generateToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');
const logger = require('../utils/logger');

// 二维码登录缓存（5分钟过期）
const qrLoginCache = new NodeCache({ stdTTL: 300 });

// 生成微信登录二维码
router.post('/qrcode', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return ResponseHelper.error(res, '缺少URL参数');
    }

    // 生成二维码图片的Base64编码
    const qrCodeDataUrl = await QRCode.toDataURL(url, {
      errorCorrectionLevel: 'H',
      color: {
        dark: '#008000', // 绿色
        light: '#FFFFFF' // 白色
      }
    });
    
    // 从URL中提取state参数并存入缓存
    let state = '';
    try {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      state = params.get('state') || '';
      
      if (state) {
        qrLoginCache.set(state, {
          created: Date.now(),
          expires: Date.now() + 5 * 60 * 1000, // 5分钟过期
          logged_in: false,
          token: null
        });
      }
    } catch (error) {
      logger.error('解析URL失败:', error);
    }
    
    ResponseHelper.success(res, {
      qrCodeUrl: qrCodeDataUrl,
      state: state
    }, '二维码生成成功');

  } catch (error) {
    logger.error('生成二维码失败', { error: error.message, body: req.body });
    ResponseHelper.serverError(res, '生成二维码失败', error);
  }
});

// 检查二维码登录状态
router.get('/qrcode/status/:state', async (req, res) => {
  try {
    const { state } = req.params;
    
    const loginData = qrLoginCache.get(state);
    if (!loginData) {
      return ResponseHelper.error(res, '二维码已过期', 410);
    }
    
    if (loginData.logged_in && loginData.token) {
      // 登录成功，清除缓存
      qrLoginCache.del(state);
      ResponseHelper.success(res, {
        status: 'success',
        token: loginData.token
      }, '登录成功');
    } else {
      ResponseHelper.success(res, {
        status: 'waiting'
      }, '等待扫码');
    }

  } catch (error) {
    logger.error('检查二维码状态失败', { error: error.message, state: req.params.state });
    ResponseHelper.serverError(res, '检查登录状态失败', error);
  }
});

// 微信授权回调处理
router.get('/callback', async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!code) {
      return res.redirect(`${process.env.FRONTEND_URL}/?error=no_code`);
    }

    // 获取微信配置
    const wechatAppId = await SystemConfig.getValue('wechat_app_id');
    const wechatAppSecret = await SystemConfig.getValue('wechat_app_secret');
    
    if (!wechatAppId || !wechatAppSecret) {
      logger.error('微信配置未设置');
      return res.redirect(`${process.env.FRONTEND_URL}/?error=wechat_config_missing`);
    }

    // 通过code获取access_token和openid
    const axios = require('axios');
    const tokenUrl = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${wechatAppId}&secret=${wechatAppSecret}&code=${code}&grant_type=authorization_code`;
    
    const tokenResponse = await axios.get(tokenUrl);
    const { openid, access_token, refresh_token, expires_in, scope } = tokenResponse.data;
    
    if (!openid) {
      logger.error('获取openid失败', tokenResponse.data);
      return res.redirect(`${process.env.FRONTEND_URL}/?error=get_openid_failed`);
    }

    // 查找或创建微信用户
    let wechatUser = await WechatUser.findByOpenid(openid);
    let user;
    
    if (wechatUser && wechatUser.userId) {
      // 已存在关联用户
      user = await User.findByPk(wechatUser.userId);
    } else {
      // 创建新用户账号
      user = await User.create({
        username: `wx_user_${openid.substring(0, 8)}`,
        password: null, // 微信登录用户无需密码
        nickname: `微信用户${openid.substring(0, 6)}`,
        isAdmin: false
      });
      
      // 创建或更新微信用户记录
      if (wechatUser) {
        wechatUser.userId = user.id;
        await wechatUser.save();
      } else {
        wechatUser = await WechatUser.create({
          openid,
          userId: user.id,
          accessToken: access_token,
          refreshToken: refresh_token,
          expiresIn: expires_in,
          scope: scope,
          lastLogin: new Date()
        });
      }
    }

    // 更新微信用户登录信息
    await wechatUser.updateLoginInfo();
    await wechatUser.updateTokens(access_token, refresh_token, expires_in);

    // 更新用户登录信息
    user.lastLoginAt = new Date();
    user.lastLoginIp = req.ip;
    await user.save();

    // 生成JWT令牌
    const token = generateToken({ id: user.id, login_type: 'wechat', openid: openid }, '60d');

    // 记录日志
    logger.logUserAction(user.id, 'wechat_login', {
      openid,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 如果是二维码登录，更新缓存状态
    if (state && qrLoginCache.has(state)) {
      qrLoginCache.set(state, {
        created: Date.now(),
        expires: Date.now() + 60 * 1000, // 1分钟后过期
        logged_in: true,
        token: token
      });
      
      // 重定向到二维码登录成功页面
      return res.redirect(`${process.env.FRONTEND_URL}/qr-success?state=${state}`);
    }

    // 普通微信登录，重定向回前端
    return res.redirect(`${process.env.FRONTEND_URL}/?token=${token}&wechat_login=success`);

  } catch (error) {
    logger.error('微信授权回调处理失败', { error: error.message, query: req.query });
    return res.redirect(`${process.env.FRONTEND_URL}/?error=callback_failed`);
  }
});

// 获取微信登录配置（公开接口）
router.get('/config', async (req, res) => {
  try {
    const wechatAppId = await SystemConfig.getValue('wechat_app_id');
    const configured = !!wechatAppId;
    
    ResponseHelper.success(res, {
      configured,
      appId: configured ? wechatAppId : null
    }, '获取微信配置成功');

  } catch (error) {
    logger.error('获取微信配置失败', { error: error.message });
    ResponseHelper.serverError(res, '获取微信配置失败', error);
  }
});

// 生成微信授权URL
router.post('/auth-url', async (req, res) => {
  try {
    const { redirectUri, state, scope = 'snsapi_base' } = req.body;
    
    const wechatAppId = await SystemConfig.getValue('wechat_app_id');
    if (!wechatAppId) {
      return ResponseHelper.error(res, '微信配置未设置');
    }

    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechatAppId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;

    ResponseHelper.success(res, { authUrl }, '生成授权URL成功');

  } catch (error) {
    logger.error('生成微信授权URL失败', { error: error.message, body: req.body });
    ResponseHelper.serverError(res, '生成授权URL失败', error);
  }
});

module.exports = router;
