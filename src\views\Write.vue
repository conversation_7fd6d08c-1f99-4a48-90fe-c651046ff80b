<template>
  <div class="write-page">
    <div class="container mx-auto px-4 py-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">写留言</h1>
      
      <div class="card">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label class="form-label">留言者名字 *</label>
            <input
              v-model="form.senderName"
              type="text"
              class="input"
              placeholder="请输入你的名字"
              required
              maxlength="50"
            >
          </div>
          
          <div>
            <label class="form-label">暗号 *</label>
            <input
              v-model="form.secretCode"
              type="text"
              class="input"
              placeholder="设置一个暗号，方便朋友找到你的留言"
              required
              maxlength="100"
            >
            <p class="text-xs text-gray-500 mt-1">朋友需要通过你的名字和暗号才能找到这条留言</p>
          </div>
          
          <div>
            <label class="form-label">留言内容 *</label>
            <textarea
              v-model="form.content"
              class="textarea"
              rows="6"
              placeholder="写下你想说的话..."
              required
              :maxlength="maxLength"
            ></textarea>
            <div class="flex justify-between items-center mt-1">
              <p class="text-xs text-gray-500">支持换行，最多{{ maxLength }}个字符</p>
              <span class="text-xs text-gray-500">{{ form.content.length }}/{{ maxLength }}</span>
            </div>
          </div>
          
          <div class="flex items-center">
            <input
              v-model="form.isPublic"
              type="checkbox"
              id="isPublic"
              class="mr-2"
            >
            <label for="isPublic" class="text-sm text-gray-700">
              公开留言（其他人可以在公开留言页面看到）
            </label>
          </div>
          
          <div class="flex space-x-4">
            <button
              type="submit"
              class="btn btn-primary flex-1"
              :disabled="submitting || !isFormValid"
            >
              <LoadingSpinner v-if="submitting" size="small" color="white" />
              <span v-else>发布留言</span>
            </button>
            
            <button
              type="button"
              class="btn btn-secondary"
              @click="resetForm"
            >
              重置
            </button>
          </div>
        </form>
      </div>
      
      <!-- 用户余额提示 -->
      <div v-if="isPaymentEnabled && user" class="card mt-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-medium text-gray-900">留言余额</h3>
            <p class="text-sm text-gray-600">当前余额：{{ user.messageBalance }} 条</p>
          </div>
          <router-link to="/payment" class="btn btn-outline btn-sm">
            购买留言包
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'Write',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const form = ref({
      senderName: '',
      secretCode: '',
      content: '',
      isPublic: false
    })
    
    const submitting = ref(false)
    
    // 计算属性
    const user = computed(() => store.getters['auth/user'])
    const maxLength = computed(() => store.getters['config/maxMessageLength'] || 500)
    const isPaymentEnabled = computed(() => store.getters['config/isPaymentEnabled'])
    
    const isFormValid = computed(() => {
      return form.value.senderName.trim() && 
             form.value.secretCode.trim() && 
             form.value.content.trim() &&
             form.value.content.length <= maxLength.value
    })
    
    // 方法
    const handleSubmit = async () => {
      if (!isFormValid.value) {
        store.dispatch('toast/error', '请填写完整信息')
        return
      }
      
      // 检查余额
      if (isPaymentEnabled.value && user.value.messageBalance <= 0) {
        store.dispatch('toast/error', '留言余额不足，请购买留言包')
        return
      }
      
      submitting.value = true
      
      try {
        await store.dispatch('messages/createMessage', {
          senderName: form.value.senderName.trim(),
          secretCode: form.value.secretCode.trim(),
          content: form.value.content.trim(),
          isPublic: form.value.isPublic
        })
        
        store.dispatch('toast/success', '留言发布成功')
        
        // 重置表单
        resetForm()
        
        // 跳转到首页
        router.push('/')
      } catch (error) {
        store.dispatch('toast/error', error.message || '发布失败')
      } finally {
        submitting.value = false
      }
    }
    
    const resetForm = () => {
      form.value = {
        senderName: '',
        secretCode: '',
        content: '',
        isPublic: false
      }
    }
    
    return {
      form,
      submitting,
      user,
      maxLength,
      isPaymentEnabled,
      isFormValid,
      handleSubmit,
      resetForm
    }
  }
}
</script>

<style scoped>
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.btn-sm {
  @apply px-3 py-1 text-sm;
}
</style>
