const { DataTypes, Model } = require('sequelize');

class MessagePackage extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '套餐名称'
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '套餐描述'
      },
      messageCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '留言数量'
      },
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: '价格（元）'
      },
      originalPrice: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: '原价（元）'
      },
      discount: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: true,
        comment: '折扣（0-1）'
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: '是否启用'
      },
      isRecommended: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否推荐'
      },
      sortOrder: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '排序'
      },
      validDays: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '有效天数（null表示永久）'
      },
      maxPurchaseCount: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '最大购买次数（null表示无限制）'
      },
      purchaseCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '已购买次数'
      },
      tags: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '标签'
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '创建者ID'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'MessagePackage',
      tableName: 'message_packages',
      indexes: [
        {
          fields: ['isActive']
        },
        {
          fields: ['sortOrder']
        },
        {
          fields: ['price']
        }
      ]
    };
  }

  // 静态方法
  static async getActivePackages() {
    return await this.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC'], ['price', 'ASC']]
    });
  }

  static async getRecommendedPackages() {
    return await this.findAll({
      where: {
        isActive: true,
        isRecommended: true
      },
      order: [['sortOrder', 'ASC'], ['price', 'ASC']]
    });
  }

  static async createDefaultPackages() {
    const defaultPackages = [
      {
        name: '体验包',
        description: '适合新用户体验',
        messageCount: 1,
        price: 1.00,
        sortOrder: 1,
        tags: ['新用户', '体验']
      },
      {
        name: '基础包',
        description: '日常使用，性价比高',
        messageCount: 10,
        price: 6.00,
        originalPrice: 10.00,
        sortOrder: 2,
        isRecommended: true,
        tags: ['推荐', '性价比']
      },
      {
        name: '标准包',
        description: '更多留言，更多表达',
        messageCount: 20,
        price: 9.00,
        originalPrice: 20.00,
        sortOrder: 3,
        tags: ['热门']
      },
      {
        name: '豪华包',
        description: '畅享留言，无限表达',
        messageCount: 50,
        price: 19.00,
        originalPrice: 50.00,
        sortOrder: 4,
        tags: ['超值', '豪华']
      }
    ];

    return await this.bulkCreate(defaultPackages, {
      ignoreDuplicates: true
    });
  }

  // 实例方法
  async incrementPurchaseCount() {
    this.purchaseCount += 1;
    await this.save();
  }

  getDiscountedPrice() {
    if (this.discount && this.originalPrice) {
      return this.originalPrice * this.discount;
    }
    return this.price;
  }

  getSavings() {
    if (this.originalPrice && this.originalPrice > this.price) {
      return this.originalPrice - this.price;
    }
    return 0;
  }

  getDiscountPercentage() {
    if (this.originalPrice && this.originalPrice > this.price) {
      return Math.round((1 - this.price / this.originalPrice) * 100);
    }
    return 0;
  }

  canPurchase() {
    if (!this.isActive) return false;
    if (this.maxPurchaseCount && this.purchaseCount >= this.maxPurchaseCount) {
      return false;
    }
    return true;
  }

  toJSON() {
    const values = { ...this.get() };
    values.discountedPrice = this.getDiscountedPrice();
    values.savings = this.getSavings();
    values.discountPercentage = this.getDiscountPercentage();
    values.canPurchase = this.canPurchase();
    return values;
  }
}

module.exports = MessagePackage;
