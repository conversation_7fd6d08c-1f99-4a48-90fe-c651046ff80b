<template>
  <div class="admin-layout">
    <div class="admin-header">
      <h1 class="text-xl font-bold text-white">留言墙管理后台</h1>
      <button @click="logout" class="text-white hover:text-gray-200">
        退出登录
      </button>
    </div>
    
    <div class="admin-content">
      <nav class="admin-sidebar">
        <router-link to="/admin/dashboard" class="nav-item">
          <i class="iconfont icon-dashboard"></i>
          仪表板
        </router-link>
        <router-link to="/admin/users" class="nav-item">
          <i class="iconfont icon-user"></i>
          用户管理
        </router-link>
        <router-link to="/admin/messages" class="nav-item">
          <i class="iconfont icon-message"></i>
          留言管理
        </router-link>
        <router-link to="/admin/sensitive-words" class="nav-item">
          <i class="iconfont icon-filter"></i>
          敏感词管理
        </router-link>
        <router-link to="/admin/config" class="nav-item">
          <i class="iconfont icon-setting"></i>
          系统配置
        </router-link>
        <router-link to="/admin/payment" class="nav-item">
          <i class="iconfont icon-payment"></i>
          支付管理
        </router-link>
      </nav>
      
      <main class="admin-main">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'AdminLayout',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const logout = () => {
      store.dispatch('toast/success', '已退出登录')
      router.push('/admin/login')
    }
    
    return {
      logout
    }
  }
}
</script>

<style scoped>
.admin-layout {
  @apply min-h-screen bg-gray-100;
}

.admin-header {
  @apply bg-gray-800 text-white px-6 py-4 flex items-center justify-between;
}

.admin-content {
  @apply flex;
}

.admin-sidebar {
  @apply w-64 bg-white shadow-sm min-h-screen p-4 space-y-2;
}

.nav-item {
  @apply flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors;
  text-decoration: none;
}

.nav-item.router-link-active {
  @apply bg-green-100 text-green-700;
}

.nav-item i {
  @apply mr-3 text-lg;
}

.admin-main {
  @apply flex-1 p-6;
}
</style>
