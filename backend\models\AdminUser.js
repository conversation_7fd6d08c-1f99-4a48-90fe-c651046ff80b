const { DataTypes, Model } = require('sequelize');
const bcrypt = require('bcryptjs');

class AdminUser extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '管理员用户名'
      },
      password: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '密码（加密后）'
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
        validate: {
          isEmail: true
        },
        comment: '邮箱'
      },
      realName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '真实姓名'
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '手机号'
      },
      avatar: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '头像URL'
      },
      role: {
        type: DataTypes.ENUM('super_admin', 'admin', 'moderator'),
        defaultValue: 'admin',
        comment: '角色'
      },
      permissions: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '权限列表'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'locked'),
        defaultValue: 'active',
        comment: '状态'
      },
      lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '最后登录时间'
      },
      lastLoginIp: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: '最后登录IP'
      },
      loginCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '登录次数'
      },
      failedLoginCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '失败登录次数'
      },
      lastFailedLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '最后失败登录时间'
      },
      lockUntil: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '锁定到期时间'
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '创建者ID'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'AdminUser',
      tableName: 'admin_users',
      hooks: {
        beforeCreate: async (admin) => {
          if (admin.password) {
            admin.password = await bcrypt.hash(admin.password, 10);
          }
        },
        beforeUpdate: async (admin) => {
          if (admin.changed('password') && admin.password) {
            admin.password = await bcrypt.hash(admin.password, 10);
          }
        }
      },
      indexes: [
        {
          fields: ['username']
        },
        {
          fields: ['email']
        },
        {
          fields: ['status']
        },
        {
          fields: ['role']
        }
      ]
    };
  }

  // 实例方法
  async validatePassword(password) {
    return await bcrypt.compare(password, this.password);
  }

  async updateLoginInfo(ip) {
    this.lastLoginAt = new Date();
    this.lastLoginIp = ip;
    this.loginCount += 1;
    this.failedLoginCount = 0; // 重置失败次数
    await this.save();
  }

  async recordFailedLogin() {
    this.failedLoginCount += 1;
    this.lastFailedLoginAt = new Date();

    // 如果失败次数超过5次，锁定账户1小时
    if (this.failedLoginCount >= 5) {
      this.status = 'locked';
      this.lockUntil = new Date(Date.now() + 60 * 60 * 1000); // 1小时后解锁
    }

    await this.save();
  }

  isLocked() {
    if (this.status === 'locked') {
      if (this.lockUntil && new Date() > this.lockUntil) {
        // 锁定时间已过，自动解锁
        this.status = 'active';
        this.lockUntil = null;
        this.failedLoginCount = 0;
        this.save();
        return false;
      }
      return true;
    }
    return false;
  }

  hasPermission(permission) {
    if (this.role === 'super_admin') return true;
    if (!this.permissions) return false;
    return this.permissions.includes(permission);
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }

  // 静态方法
  static async findByUsername(username) {
    return await this.findOne({ where: { username } });
  }

  static async createDefaultAdmin() {
    const adminExists = await this.findOne({ where: { role: 'super_admin' } });
    if (!adminExists) {
      return await this.create({
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        realName: '超级管理员',
        role: 'super_admin',
        permissions: ['*'], // 所有权限
        status: 'active'
      });
    }
    return adminExists;
  }

  static getDefaultPermissions() {
    return {
      super_admin: ['*'],
      admin: [
        'user.view', 'user.edit', 'user.delete',
        'message.view', 'message.edit', 'message.delete', 'message.audit',
        'sensitive_word.view', 'sensitive_word.edit', 'sensitive_word.delete',
        'payment.view', 'payment.edit',
        'config.view', 'config.edit'
      ],
      moderator: [
        'message.view', 'message.audit',
        'sensitive_word.view'
      ]
    };
  }
}

module.exports = AdminUser;
