<template>
  <div class="message-detail-page">
    <div class="container mx-auto px-4 py-6">
      <div v-if="loading" class="text-center py-8">
        <LoadingSpinner />
      </div>
      
      <div v-else-if="message" class="card">
        <div class="flex items-start mb-4">
          <div class="avatar mr-3">
            {{ message.senderName.charAt(0) }}
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-semibold text-gray-900">{{ message.senderName }}</h2>
            <p class="text-sm text-gray-500">{{ formatTime(message.createdAt) }}</p>
          </div>
        </div>
        
        <div class="mb-6">
          <p class="text-gray-800 whitespace-pre-wrap">{{ message.content }}</p>
        </div>
        
        <div class="flex items-center justify-between border-t pt-4">
          <div class="flex items-center text-sm text-gray-500">
            <span class="mr-4">
              <i class="iconfont icon-eye mr-1"></i>
              {{ message.viewCount }} 次查看
            </span>
            <span>
              <i class="iconfont icon-heart mr-1"></i>
              {{ message.likeCount }} 个赞
            </span>
          </div>
          
          <button
            v-if="user"
            @click="toggleLike"
            class="btn btn-outline btn-sm"
            :class="{ 'text-red-500 border-red-500': message.liked }"
          >
            <i class="iconfont icon-heart mr-1"></i>
            {{ message.liked ? '已赞' : '点赞' }}
          </button>
        </div>
      </div>
      
      <div v-else class="text-center py-12">
        <p class="text-gray-500">留言不存在或已被删除</p>
        <router-link to="/" class="btn btn-primary mt-4">
          返回首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { smartFormatTime } from '../utils/date'

export default {
  name: 'MessageDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()
    
    const loading = ref(false)
    
    // 计算属性
    const user = computed(() => store.getters['auth/user'])
    const message = computed(() => store.getters['messages/currentMessage'])
    
    // 方法
    const fetchMessage = async () => {
      loading.value = true
      try {
        await store.dispatch('messages/fetchMessage', route.params.id)
      } catch (error) {
        store.dispatch('toast/error', error.message || '获取留言失败')
      } finally {
        loading.value = false
      }
    }
    
    const toggleLike = async () => {
      if (!user.value) {
        store.dispatch('toast/warning', '请先登录')
        router.push('/login')
        return
      }
      
      try {
        await store.dispatch('messages/toggleLike', message.value.id)
      } catch (error) {
        store.dispatch('toast/error', error.message || '操作失败')
      }
    }
    
    const formatTime = (time) => {
      return smartFormatTime(new Date(time))
    }
    
    // 生命周期
    onMounted(() => {
      fetchMessage()
    })
    
    return {
      loading,
      user,
      message,
      toggleLike,
      formatTime
    }
  }
}
</script>

<style scoped>
.btn-sm {
  @apply px-3 py-1 text-sm;
}
</style>
