const { DataTypes, Model } = require('sequelize');

class SensitiveWord extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      word: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '敏感词'
      },
      category: {
        type: DataTypes.ENUM('political', 'violence', 'pornography', 'gambling', 'drugs', 'other'),
        defaultValue: 'other',
        comment: '敏感词类型'
      },
      level: {
        type: DataTypes.ENUM('low', 'medium', 'high'),
        defaultValue: 'medium',
        comment: '敏感级别'
      },
      action: {
        type: DataTypes.ENUM('replace', 'block', 'review'),
        defaultValue: 'replace',
        comment: '处理动作'
      },
      replacement: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '替换词'
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: '是否启用'
      },
      hitCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '命中次数'
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '创建者ID'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'SensitiveWord',
      tableName: 'sensitive_words',
      indexes: [
        {
          fields: ['word']
        },
        {
          fields: ['category']
        },
        {
          fields: ['isActive']
        }
      ]
    };
  }

  // 静态方法
  static async getActiveWords() {
    return await this.findAll({
      where: { isActive: true },
      order: [['word', 'ASC']]
    });
  }

  static async findByCategory(category) {
    return await this.findAll({
      where: {
        category,
        isActive: true
      },
      order: [['word', 'ASC']]
    });
  }

  static async incrementHitCount(wordId) {
    const word = await this.findByPk(wordId);
    if (word) {
      word.hitCount += 1;
      await word.save();
    }
  }

  static async batchCreate(words) {
    return await this.bulkCreate(words, {
      ignoreDuplicates: true
    });
  }

  // 敏感词检测方法
  static async checkContent(content) {
    const words = await this.getActiveWords();
    const foundWords = [];
    let filteredContent = content;

    for (const wordObj of words) {
      const word = wordObj.word;
      const regex = new RegExp(word, 'gi');

      if (regex.test(content)) {
        foundWords.push({
          word: word,
          category: wordObj.category,
          level: wordObj.level,
          action: wordObj.action
        });

        // 增加命中次数
        await this.incrementHitCount(wordObj.id);

        // 根据动作处理内容
        if (wordObj.action === 'replace') {
          const replacement = wordObj.replacement || '*'.repeat(word.length);
          filteredContent = filteredContent.replace(regex, replacement);
        }
      }
    }

    return {
      hasWords: foundWords.length > 0,
      foundWords,
      filteredContent,
      originalContent: content
    };
  }
}

module.exports = SensitiveWord;
