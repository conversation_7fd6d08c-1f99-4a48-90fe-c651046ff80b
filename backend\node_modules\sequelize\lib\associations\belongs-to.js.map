{"version": 3, "sources": ["../../src/associations/belongs-to.js"], "sourcesContent": ["'use strict';\n\nconst Utils = require('./../utils');\nconst Helpers = require('./helpers');\nconst _ = require('lodash');\nconst Association = require('./base');\nconst Op = require('../operators');\n\n/**\n * One-to-one association\n *\n * In the API reference below, add the name of the association to the method, e.g. for `User.belongsTo(Project)` the getter will be `user.getProject()`.\n *\n * @see {@link Model.belongsTo}\n */\nclass BelongsTo extends Association {\n  constructor(source, target, options) {\n    super(source, target, options);\n\n    this.associationType = 'BelongsTo';\n    this.isSingleAssociation = true;\n    this.foreignKeyAttribute = {};\n\n    if (this.as) {\n      this.isAliased = true;\n      this.options.name = {\n        singular: this.as\n      };\n    } else {\n      this.as = this.target.options.name.singular;\n      this.options.name = this.target.options.name;\n    }\n\n    if (_.isObject(this.options.foreignKey)) {\n      this.foreignKeyAttribute = this.options.foreignKey;\n      this.foreignKey = this.foreignKeyAttribute.name || this.foreignKeyAttribute.fieldName;\n    } else if (this.options.foreignKey) {\n      this.foreignKey = this.options.foreignKey;\n    }\n\n    if (!this.foreignKey) {\n      this.foreignKey = Utils.camelize(\n        [\n          this.as,\n          this.target.primaryKeyAttribute\n        ].join('_')\n      );\n    }\n\n    this.identifier = this.foreignKey;\n    if (this.source.rawAttributes[this.identifier]) {\n      this.identifierField = this.source.rawAttributes[this.identifier].field || this.identifier;\n    }\n\n    if (\n      this.options.targetKey\n      && !this.target.rawAttributes[this.options.targetKey]\n    ) {\n      throw new Error(`Unknown attribute \"${this.options.targetKey}\" passed as targetKey, define this attribute on model \"${this.target.name}\" first`);\n    }\n\n    this.targetKey = this.options.targetKey || this.target.primaryKeyAttribute;\n    this.targetKeyField = this.target.rawAttributes[this.targetKey].field || this.targetKey;\n    this.targetKeyIsPrimary = this.targetKey === this.target.primaryKeyAttribute;\n    this.targetIdentifier = this.targetKey;\n\n    this.associationAccessor = this.as;\n    this.options.useHooks = options.useHooks;\n\n    // Get singular name, trying to uppercase the first letter, unless the model forbids it\n    const singular = _.upperFirst(this.options.name.singular);\n\n    this.accessors = {\n      get: `get${singular}`,\n      set: `set${singular}`,\n      create: `create${singular}`\n    };\n  }\n\n  // the id is in the source table\n  _injectAttributes() {\n    const newAttributes = {\n      [this.foreignKey]: {\n        type: this.options.keyType || this.target.rawAttributes[this.targetKey].type,\n        allowNull: true,\n        ...this.foreignKeyAttribute\n      }\n    };\n\n    if (this.options.constraints !== false) {\n      const source = this.source.rawAttributes[this.foreignKey] || newAttributes[this.foreignKey];\n      this.options.onDelete = this.options.onDelete || (source.allowNull ? 'SET NULL' : 'NO ACTION');\n      this.options.onUpdate = this.options.onUpdate || 'CASCADE';\n    }\n\n    Helpers.addForeignKeyConstraints(newAttributes[this.foreignKey], this.target, this.source, this.options, this.targetKeyField);\n    Utils.mergeDefaults(this.source.rawAttributes, newAttributes);\n\n    this.source.refreshAttributes();\n\n    this.identifierField = this.source.rawAttributes[this.foreignKey].field || this.foreignKey;\n\n    Helpers.checkNamingCollision(this);\n\n    return this;\n  }\n\n  mixin(obj) {\n    const methods = ['get', 'set', 'create'];\n\n    Helpers.mixinMethods(this, obj, methods);\n  }\n\n  /**\n   * Get the associated instance.\n   *\n   * @param {Model|Array<Model>} instances source instances\n   * @param {object}         [options] find options\n   * @param {string|boolean} [options.scope]  Apply a scope on the related model, or remove its default scope by passing false.\n   * @param {string}         [options.schema] Apply a schema on the related model\n   *\n   * @see\n   * {@link Model.findOne} for a full explanation of options\n   *\n   * @returns {Promise<Model>}\n   */\n  async get(instances, options) {\n    const where = {};\n    let Target = this.target;\n    let instance;\n\n    options = Utils.cloneDeep(options);\n\n    if (Object.prototype.hasOwnProperty.call(options, 'scope')) {\n      if (!options.scope) {\n        Target = Target.unscoped();\n      } else {\n        Target = Target.scope(options.scope);\n      }\n    }\n\n    if (Object.prototype.hasOwnProperty.call(options, 'schema')) {\n      Target = Target.schema(options.schema, options.schemaDelimiter);\n    }\n\n    if (!Array.isArray(instances)) {\n      instance = instances;\n      instances = undefined;\n    }\n\n    if (instances) {\n      where[this.targetKey] = {\n        [Op.in]: instances.map(_instance => _instance.get(this.foreignKey))\n      };\n    } else {\n      if (this.targetKeyIsPrimary && !options.where) {\n        return Target.findByPk(instance.get(this.foreignKey), options);\n      }\n      where[this.targetKey] = instance.get(this.foreignKey);\n      options.limit = null;\n    }\n\n    options.where = options.where ?\n      { [Op.and]: [where, options.where] } :\n      where;\n\n    if (instances) {\n      const results = await Target.findAll(options);\n      const result = {};\n      for (const _instance of instances) {\n        result[_instance.get(this.foreignKey, { raw: true })] = null;\n      }\n\n      for (const _instance of results) {\n        result[_instance.get(this.targetKey, { raw: true })] = _instance;\n      }\n\n      return result;\n    }\n\n    return Target.findOne(options);\n  }\n\n  /**\n   * Set the associated model.\n   *\n   * @param {Model} sourceInstance the source instance\n   * @param {?Model|string|number} [associatedInstance] An persisted instance or the primary key of an instance to associate with this. Pass `null` or `undefined` to remove the association.\n   * @param {object} [options={}] options passed to `this.save`\n   * @param {boolean} [options.save=true] Skip saving this after setting the foreign key if false.\n   *\n   *  @returns {Promise}\n   */\n  async set(sourceInstance, associatedInstance, options = {}) {\n    let value = associatedInstance;\n\n    if (associatedInstance instanceof this.target) {\n      value = associatedInstance[this.targetKey];\n    }\n\n    sourceInstance.set(this.foreignKey, value);\n\n    if (options.save === false) return;\n\n    options = {\n      fields: [this.foreignKey],\n      allowNull: [this.foreignKey],\n      association: true,\n      ...options\n    };\n\n    // passes the changed field to save, so only that field get updated.\n    return await sourceInstance.save(options);\n  }\n\n  /**\n   * Create a new instance of the associated model and associate it with this.\n   *\n   * @param {Model} sourceInstance the source instance\n   * @param {object} [values={}] values to create associated model instance with\n   * @param {object} [options={}] Options passed to `target.create` and setAssociation.\n   *\n   * @see\n   * {@link Model#create}  for a full explanation of options\n   *\n   * @returns {Promise<Model>} The created target model\n   */\n  async create(sourceInstance, values, options) {\n    values = values || {};\n    options = options || {};\n\n    const newAssociatedObject = await this.target.create(values, options);\n    await sourceInstance[this.accessors.set](newAssociatedObject, options);\n\n    return newAssociatedObject;\n  }\n\n  verifyAssociationAlias(alias) {\n    if (typeof alias === 'string') {\n      return this.as === alias;\n    }\n\n    if (alias && alias.singular) {\n      return this.as === alias.singular;\n    }\n\n    return !this.isAliased;\n  }\n}\n\nmodule.exports = BelongsTo;\nmodule.exports.BelongsTo = BelongsTo;\nmodule.exports.default = BelongsTo;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,QAAQ,QAAQ;AACtB,MAAM,UAAU,QAAQ;AACxB,MAAM,IAAI,QAAQ;AAClB,MAAM,cAAc,QAAQ;AAC5B,MAAM,KAAK,QAAQ;AASnB,wBAAwB,YAAY;AAAA,EAClC,YAAY,QAAQ,QAAQ,SAAS;AACnC,UAAM,QAAQ,QAAQ;AAEtB,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;AAE3B,QAAI,KAAK,IAAI;AACX,WAAK,YAAY;AACjB,WAAK,QAAQ,OAAO;AAAA,QAClB,UAAU,KAAK;AAAA;AAAA,WAEZ;AACL,WAAK,KAAK,KAAK,OAAO,QAAQ,KAAK;AACnC,WAAK,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA;AAG1C,QAAI,EAAE,SAAS,KAAK,QAAQ,aAAa;AACvC,WAAK,sBAAsB,KAAK,QAAQ;AACxC,WAAK,aAAa,KAAK,oBAAoB,QAAQ,KAAK,oBAAoB;AAAA,eACnE,KAAK,QAAQ,YAAY;AAClC,WAAK,aAAa,KAAK,QAAQ;AAAA;AAGjC,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,MAAM,SACtB;AAAA,QACE,KAAK;AAAA,QACL,KAAK,OAAO;AAAA,QACZ,KAAK;AAAA;AAIX,SAAK,aAAa,KAAK;AACvB,QAAI,KAAK,OAAO,cAAc,KAAK,aAAa;AAC9C,WAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAAA;AAGlF,QACE,KAAK,QAAQ,aACV,CAAC,KAAK,OAAO,cAAc,KAAK,QAAQ,YAC3C;AACA,YAAM,IAAI,MAAM,sBAAsB,KAAK,QAAQ,mEAAmE,KAAK,OAAO;AAAA;AAGpI,SAAK,YAAY,KAAK,QAAQ,aAAa,KAAK,OAAO;AACvD,SAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAC9E,SAAK,qBAAqB,KAAK,cAAc,KAAK,OAAO;AACzD,SAAK,mBAAmB,KAAK;AAE7B,SAAK,sBAAsB,KAAK;AAChC,SAAK,QAAQ,WAAW,QAAQ;AAGhC,UAAM,WAAW,EAAE,WAAW,KAAK,QAAQ,KAAK;AAEhD,SAAK,YAAY;AAAA,MACf,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,QAAQ,SAAS;AAAA;AAAA;AAAA,EAKrB,oBAAoB;AAClB,UAAM,gBAAgB;AAAA,OACnB,KAAK,aAAa;AAAA,QACjB,MAAM,KAAK,QAAQ,WAAW,KAAK,OAAO,cAAc,KAAK,WAAW;AAAA,QACxE,WAAW;AAAA,SACR,KAAK;AAAA;AAIZ,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,YAAM,SAAS,KAAK,OAAO,cAAc,KAAK,eAAe,cAAc,KAAK;AAChF,WAAK,QAAQ,WAAW,KAAK,QAAQ,YAAa,QAAO,YAAY,aAAa;AAClF,WAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY;AAAA;AAGnD,YAAQ,yBAAyB,cAAc,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9G,UAAM,cAAc,KAAK,OAAO,eAAe;AAE/C,SAAK,OAAO;AAEZ,SAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAEhF,YAAQ,qBAAqB;AAE7B,WAAO;AAAA;AAAA,EAGT,MAAM,KAAK;AACT,UAAM,UAAU,CAAC,OAAO,OAAO;AAE/B,YAAQ,aAAa,MAAM,KAAK;AAAA;AAAA,QAgB5B,IAAI,WAAW,SAAS;AAC5B,UAAM,QAAQ;AACd,QAAI,SAAS,KAAK;AAClB,QAAI;AAEJ,cAAU,MAAM,UAAU;AAE1B,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,UAAU;AAC1D,UAAI,CAAC,QAAQ,OAAO;AAClB,iBAAS,OAAO;AAAA,aACX;AACL,iBAAS,OAAO,MAAM,QAAQ;AAAA;AAAA;AAIlC,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,WAAW;AAC3D,eAAS,OAAO,OAAO,QAAQ,QAAQ,QAAQ;AAAA;AAGjD,QAAI,CAAC,MAAM,QAAQ,YAAY;AAC7B,iBAAW;AACX,kBAAY;AAAA;AAGd,QAAI,WAAW;AACb,YAAM,KAAK,aAAa;AAAA,SACrB,GAAG,KAAK,UAAU,IAAI,eAAa,UAAU,IAAI,KAAK;AAAA;AAAA,WAEpD;AACL,UAAI,KAAK,sBAAsB,CAAC,QAAQ,OAAO;AAC7C,eAAO,OAAO,SAAS,SAAS,IAAI,KAAK,aAAa;AAAA;AAExD,YAAM,KAAK,aAAa,SAAS,IAAI,KAAK;AAC1C,cAAQ,QAAQ;AAAA;AAGlB,YAAQ,QAAQ,QAAQ,QACtB,GAAG,GAAG,MAAM,CAAC,OAAO,QAAQ,WAC5B;AAEF,QAAI,WAAW;AACb,YAAM,UAAU,MAAM,OAAO,QAAQ;AACrC,YAAM,SAAS;AACf,iBAAW,aAAa,WAAW;AACjC,eAAO,UAAU,IAAI,KAAK,YAAY,EAAE,KAAK,WAAW;AAAA;AAG1D,iBAAW,aAAa,SAAS;AAC/B,eAAO,UAAU,IAAI,KAAK,WAAW,EAAE,KAAK,WAAW;AAAA;AAGzD,aAAO;AAAA;AAGT,WAAO,OAAO,QAAQ;AAAA;AAAA,QAalB,IAAI,gBAAgB,oBAAoB,UAAU,IAAI;AAC1D,QAAI,QAAQ;AAEZ,QAAI,8BAA8B,KAAK,QAAQ;AAC7C,cAAQ,mBAAmB,KAAK;AAAA;AAGlC,mBAAe,IAAI,KAAK,YAAY;AAEpC,QAAI,QAAQ,SAAS;AAAO;AAE5B,cAAU;AAAA,MACR,QAAQ,CAAC,KAAK;AAAA,MACd,WAAW,CAAC,KAAK;AAAA,MACjB,aAAa;AAAA,OACV;AAIL,WAAO,MAAM,eAAe,KAAK;AAAA;AAAA,QAe7B,OAAO,gBAAgB,QAAQ,SAAS;AAC5C,aAAS,UAAU;AACnB,cAAU,WAAW;AAErB,UAAM,sBAAsB,MAAM,KAAK,OAAO,OAAO,QAAQ;AAC7D,UAAM,eAAe,KAAK,UAAU,KAAK,qBAAqB;AAE9D,WAAO;AAAA;AAAA,EAGT,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,KAAK,OAAO;AAAA;AAGrB,QAAI,SAAS,MAAM,UAAU;AAC3B,aAAO,KAAK,OAAO,MAAM;AAAA;AAG3B,WAAO,CAAC,KAAK;AAAA;AAAA;AAIjB,OAAO,UAAU;AACjB,OAAO,QAAQ,YAAY;AAC3B,OAAO,QAAQ,UAAU;", "names": []}