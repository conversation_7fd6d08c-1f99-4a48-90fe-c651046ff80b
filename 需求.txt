## 项目概述

留言墙应用是一个基于H5移动端的交互式平台，允许用户创建、查看和管理留言。该应用包含公开留言墙和私人留言功能，支持点赞、评论、敏感词过滤和管理员后台审核等特性。

### 主要功能：

- 用户留言发布与查看，输入好友名字或暗号以及留言内容即可提交留言
- 公开/私密留言设置
- 留言搜索（按名字或者暗号）
- 留言点赞
- 敏感词自动检测与过滤
- 管理员后台（敏感词管理、留言审核）

## 技术栈
### 前端
- 使用Vue.js
- 响应式设计（移动优先）
-API调用：可以使用Axios或Fetch来发送HTTP请求，与后端的API进行交互。通过封装API请求函数，可以方便地在组件中调用后端接口，获取或提交数据。
-UI 框架：采用 Tailwind CSS 来快速搭建美观且响应式的界面。它提供了大量实用的 CSS 类，可直接应用于 HTML 元素，减少自定义 CSS 代码的编写。
-优先选择国内CDN加载UI框架和图标库。
-状态管理：可搭配 Vuex 管理应用的状态；

-依赖的安装尽量使用国内资源来加速下载


### 后端
- Node.js
- Express.js (Web服务框架)
- MySQL (数据存储)
- API设计：采用RESTful API风格进行设计，通过HTTP请求的GET、POST、PUT、DELETE等方法来实现对资源的增删改查操作。例如，获取留言列表可以使用GET请求  /api/messages  ，提交新留言可以使用POST请求  /api/messages  
-身份认证：可以使用JWT（JSON Web Token）进行用户身份认证。用户登录成功后，后端生成一个JWT令牌并返回给前端，前端将令牌存储在本地存储中，并在后续的请求中携带该令牌，后端通过验证令牌来确认用户的身份。
-依赖的安装尽量使用国内资源来加速下载

### 工具和库
- 图标库优先使用中国国内公开图标库，访问速度快

### 数据库
-MySQL
-本地电脑已安装MySQL服务，使用密码是：qq674482179

## 前端和后台分离架构

## 页面功能：
1、底部有一个导航栏：分别是首页、找留言、写留言、公开留言、登录（未注册或登录显示“登录”，如果登录了用户账号，则显示“我的”，点击跳转到个人中心页面）

首页：
-居中有竖排的3个按钮：“找留言”、“写留言”、“公开留言”
- 一个按钮跳转到找留言
- 一个按钮跳转到写留言
- 一个按钮跳转到公开留言
- 显示最新的公开留言10条列表，每个留言包含：留言者头像、留言者名字、留言内容、留言时间、点赞数。
-有一个查看更多的按钮，点击跳转到公开留言的页面，可以看到全部的公开留言列表
-

写留言：
- 输入留言者名字、暗号和留言内容，点击发布按钮，留言发布成功后，跳转到留言详情页面。

查留言：
-输入留言者名字、暗号，查找到对应的留言内容
-如果找不到，则显示“没有找到留言”
-如果找到，则显示留言内容，并显示留言者名字、暗号、留言时间、点赞数。
-有分页功能

公开留言：
- 显示最新的公开留言10条列表，每个留言包含：留言者头像、留言者名字、留言内容、留言时间、点赞数。
-有一个查看更多的按钮，点击跳转到公开留言的页面，可以看到全部的公开留言列表
-

登录：
- 输入账号和密码，点击登录按钮，登录成功后，跳转到首页。
- 如果没有账号，则跳转到注册页面。

我的页面：
- 显示我的留言列表，每个留言包含：留言者头像、留言者名字、留言内容、留言时间、点赞数。
- 有一个查看更多的按钮，点击跳转到我的留言的页面，可以看到全部的我的留言列表
- 有一个删除留言的按钮，点击删除留言，删除留言成功后，跳转到我的留言的页面

管理员后台：
- 管理员登录页面
- 管理员登录成功后，跳转到管理员后台页面
-管理菜单导航栏在左侧；
- 管理员后台页面显示留言列表，每个留言包含：留言者头像、留言者名字、留言内容、留言时间、点赞数。
- 管理员后台页面显示敏感词列表，每个敏感词包含：敏感词、敏感词类型、敏感词状态。
- 管理员后台页面显示留言审核列表，每个留言包含：留言者头像、留言者名字、留言内容、留言时间、点赞数。


支付功能开发：
1、发布留言需要付费，使用微信支付功能；
2、微信支付功能开发：使用微信官方支付，在微信支付成功后，将微信支付订单号保存到数据库中，并返回给前端，前端根据返回的微信支付订单号，调用微信支付查询接口，获取微信支付结果，并更新数据库中的微信支付订单号状态。
3、用户发布留言，弹窗出现留言包的选择，如1元1条，6元10条，9元20条，19元50条；
4、用户选择留言包，点击确认，微信支付功能开始，用户支付成功后，微信支付订单号保存到数据库中，并返回给前端，前端根据返回的微信支付订单号，调用微信支付查询接口，获取微信支付结果，并更新数据库中的微信支付订单号状态。
5.个人中心添加一个显示留言包余额的功能，用户可以查看自己剩余的留言包余额，并选择留言包购买。
6、管理控制台添加支付管理页面，管理员可以查看所有的微信支付订单，并管理微信支付订单的状态。
7、用户可以在个人中心查看自己的微信支付订单，
8、用户可以在个人中心查看自己的留言包余额，并选择留言包购买。
9、管理控制台添加一个【支付设置】的管理菜单，用来填写微信商户号和密钥，用来保存微信支付配置；
10、管理控制台添加一个【留言包设置】的管理菜单，用来填写留言包的价格和数量，用来保存留言包的价格和数量；


【新功能】
管理控制台，在“系统设置”菜单里面，添加一个功能，用来切换控制前端用户发布留言是否免费发布还是付费发布留言；


根据需求文档，你作为专业的产品经理和专业的程序员，你来完成产品的详细开发