<template>
  <div class="qr-success-page">
    <div class="container mx-auto px-4 py-12 text-center">
      <div class="max-w-md mx-auto">
        <div class="mb-8">
          <i class="iconfont icon-check-circle text-6xl text-green-500"></i>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900 mb-4">登录成功</h1>
        <p class="text-gray-600 mb-8">微信登录成功，正在跳转...</p>
        
        <div class="space-y-4">
          <button @click="goHome" class="btn btn-primary w-full">
            进入留言墙
          </button>
          
          <p class="text-sm text-gray-500">
            如果没有自动跳转，请点击上方按钮
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'QrSuccess',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    
    const goHome = () => {
      router.push('/')
    }
    
    onMounted(() => {
      // 3秒后自动跳转
      setTimeout(() => {
        goHome()
      }, 3000)
    })
    
    return {
      goHome
    }
  }
}
</script>
</template>
