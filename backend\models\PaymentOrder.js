const { DataTypes, Model } = require('sequelize');

class PaymentOrder extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      orderNo: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '订单号'
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: '用户ID'
      },
      packageId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'message_packages',
          key: 'id'
        },
        comment: '留言包ID'
      },
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: '支付金额（分）'
      },
      messageCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '留言数量'
      },
      paymentMethod: {
        type: DataTypes.ENUM('wechat', 'alipay', 'mock'),
        allowNull: false,
        comment: '支付方式'
      },
      status: {
        type: DataTypes.ENUM('pending', 'paid', 'failed', 'cancelled', 'refunded'),
        defaultValue: 'pending',
        comment: '订单状态'
      },
      wechatOrderId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '微信订单号'
      },
      wechatTransactionId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '微信交易号'
      },
      payTime: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '支付时间'
      },
      expireTime: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '过期时间'
      },
      notifyData: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '支付回调数据'
      },
      clientIp: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: '客户端IP'
      },
      userAgent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '用户代理'
      },
      remark: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '备注'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'PaymentOrder',
      tableName: 'payment_orders',
      indexes: [
        {
          fields: ['orderNo']
        },
        {
          fields: ['userId']
        },
        {
          fields: ['status']
        },
        {
          fields: ['wechatOrderId']
        },
        {
          fields: ['wechatTransactionId']
        },
        {
          fields: ['createdAt']
        }
      ]
    };
  }

  // 静态方法
  static generateOrderNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `MSG${timestamp}${random}`;
  }

  static async createOrder(orderData) {
    const orderNo = this.generateOrderNo();
    const expireTime = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期

    return await this.create({
      ...orderData,
      orderNo,
      expireTime
    });
  }

  static async findByOrderNo(orderNo) {
    return await this.findOne({
      where: { orderNo },
      include: [
        {
          model: require('./User'),
          as: 'user',
          attributes: ['id', 'username', 'nickname']
        },
        {
          model: require('./MessagePackage'),
          as: 'package'
        }
      ]
    });
  }

  static async findUserOrders(userId, limit = 10, offset = 0) {
    return await this.findAndCountAll({
      where: { userId },
      include: [{
        model: require('./MessagePackage'),
        as: 'package'
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }

  static async findPendingOrders() {
    return await this.findAll({
      where: {
        status: 'pending',
        expireTime: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });
  }

  // 实例方法
  async markAsPaid(paymentData = {}) {
    this.status = 'paid';
    this.payTime = new Date();
    if (paymentData.wechatTransactionId) {
      this.wechatTransactionId = paymentData.wechatTransactionId;
    }
    if (paymentData.notifyData) {
      this.notifyData = paymentData.notifyData;
    }
    await this.save();
  }

  async markAsFailed(reason = '') {
    this.status = 'failed';
    this.remark = reason;
    await this.save();
  }

  async cancel() {
    this.status = 'cancelled';
    await this.save();
  }

  isExpired() {
    return this.expireTime && new Date() > this.expireTime;
  }

  isPaid() {
    return this.status === 'paid';
  }
}

module.exports = PaymentOrder;
