<template>
  <div class="public-page">
    <div class="container mx-auto px-4 py-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">公开留言</h1>
      
      <div v-if="loading" class="text-center py-8">
        <LoadingSpinner />
      </div>
      
      <div v-else-if="messages.length === 0" class="text-center py-12">
        <i class="iconfont icon-message text-4xl text-gray-400 mb-4"></i>
        <p class="text-gray-500">暂无公开留言</p>
      </div>
      
      <div v-else class="space-y-4">
        <div
          v-for="message in messages"
          :key="message.id"
          class="message-card cursor-pointer"
          @click="viewMessage(message.id)"
        >
          <div class="flex items-start">
            <div class="avatar mr-3">
              {{ message.senderName.charAt(0) }}
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-gray-900">{{ message.senderName }}</h4>
                <span class="text-xs text-gray-500">{{ formatTime(message.createdAt) }}</span>
              </div>
              <p class="text-gray-700 text-sm mb-3">{{ message.content }}</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-4">
                    <i class="iconfont icon-eye mr-1"></i>
                    {{ message.viewCount }}
                  </span>
                  <span>
                    <i class="iconfont icon-heart mr-1"></i>
                    {{ message.likeCount }}
                  </span>
                </div>
                <button
                  v-if="user"
                  @click.stop="toggleLike(message)"
                  class="flex items-center text-xs text-gray-500 hover:text-red-500 transition-colors"
                  :class="{ 'text-red-500': message.liked }"
                >
                  <i class="iconfont icon-heart mr-1"></i>
                  {{ message.liked ? '已赞' : '点赞' }}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center py-4">
          <button
            @click="loadMore"
            class="btn btn-outline"
            :disabled="loadingMore"
          >
            <LoadingSpinner v-if="loadingMore" size="small" />
            <span v-else>加载更多</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { formatDistanceToNow } from '../utils/date'

export default {
  name: 'Public',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const loading = ref(false)
    const loadingMore = ref(false)
    const page = ref(1)
    const limit = 10
    
    // 计算属性
    const user = computed(() => store.getters['auth/user'])
    const messages = computed(() => store.getters['messages/publicMessages'])
    const pagination = computed(() => store.getters['messages/pagination'])
    const hasMore = computed(() => page.value < pagination.value.totalPages)
    
    // 方法
    const fetchMessages = async (pageNum = 1, isLoadMore = false) => {
      if (isLoadMore) {
        loadingMore.value = true
      } else {
        loading.value = true
      }
      
      try {
        await store.dispatch('messages/fetchPublicMessages', {
          page: pageNum,
          limit
        })
      } catch (error) {
        store.dispatch('toast/error', error.message || '获取留言失败')
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }
    
    const loadMore = async () => {
      page.value += 1
      await fetchMessages(page.value, true)
    }
    
    const viewMessage = (id) => {
      router.push(`/message/${id}`)
    }
    
    const toggleLike = async (message) => {
      if (!user.value) {
        store.dispatch('toast/warning', '请先登录')
        router.push('/login')
        return
      }
      
      try {
        await store.dispatch('messages/toggleLike', message.id)
      } catch (error) {
        store.dispatch('toast/error', error.message || '操作失败')
      }
    }
    
    const formatTime = (time) => {
      return formatDistanceToNow(new Date(time))
    }
    
    // 生命周期
    onMounted(() => {
      fetchMessages()
    })
    
    return {
      loading,
      loadingMore,
      user,
      messages,
      hasMore,
      fetchMessages,
      loadMore,
      viewMessage,
      toggleLike,
      formatTime
    }
  }
}
</script>
</template>
