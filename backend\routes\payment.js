const express = require('express');
const router = express.Router();
const { PaymentOrder, MessagePackage, User, SystemConfig } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const { validatePagination, validateId } = require('../middleware/validation');
const ResponseHelper = require('../utils/response');
const logger = require('../utils/logger');

// 获取留言包列表
router.get('/packages', async (req, res) => {
  try {
    const packages = await MessagePackage.getActivePackages();
    ResponseHelper.success(res, packages, '获取留言包列表成功');
  } catch (error) {
    logger.error('获取留言包列表失败', { error: error.message });
    ResponseHelper.serverError(res, '获取留言包列表失败', error);
  }
});

// 创建支付订单
router.post('/orders', authenticateUser, async (req, res) => {
  try {
    const { packageId, paymentMethod = 'wechat' } = req.body;
    const userId = req.user.id;

    // 检查留言包是否存在
    const messagePackage = await MessagePackage.findByPk(packageId);
    if (!messagePackage) {
      return ResponseHelper.notFound(res, '留言包不存在');
    }

    if (!messagePackage.canPurchase()) {
      return ResponseHelper.error(res, '留言包不可购买');
    }

    // 创建订单
    const order = await PaymentOrder.createOrder({
      userId,
      packageId,
      amount: messagePackage.price,
      messageCount: messagePackage.messageCount,
      paymentMethod,
      clientIp: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 记录日志
    logger.logPayment(userId, 'create_order', {
      orderId: order.id,
      orderNo: order.orderNo,
      packageId,
      amount: order.amount
    });

    ResponseHelper.created(res, order, '订单创建成功');

  } catch (error) {
    logger.error('创建支付订单失败', { error: error.message, body: req.body, userId: req.user?.id });
    ResponseHelper.serverError(res, '创建订单失败', error);
  }
});

// 获取用户订单列表
router.get('/orders', authenticateUser, validatePagination, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.user.id;

    const result = await PaymentOrder.findUserOrders(userId, limit, offset);

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取订单列表成功');

  } catch (error) {
    logger.error('获取用户订单失败', { error: error.message, userId: req.user?.id });
    ResponseHelper.serverError(res, '获取订单列表失败', error);
  }
});

// 获取订单详情
router.get('/orders/:orderNo', authenticateUser, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await PaymentOrder.findByOrderNo(orderNo);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 检查权限
    if (order.userId !== userId) {
      return ResponseHelper.forbidden(res, '无权访问此订单');
    }

    ResponseHelper.success(res, order, '获取订单详情成功');

  } catch (error) {
    logger.error('获取订单详情失败', { error: error.message, orderNo: req.params.orderNo, userId: req.user?.id });
    ResponseHelper.serverError(res, '获取订单详情失败', error);
  }
});

// 发起支付
router.post('/orders/:orderNo/pay', authenticateUser, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await PaymentOrder.findByOrderNo(orderNo);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 检查权限
    if (order.userId !== userId) {
      return ResponseHelper.forbidden(res, '无权操作此订单');
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return ResponseHelper.error(res, '订单状态不正确');
    }

    if (order.isExpired()) {
      await order.cancel();
      return ResponseHelper.error(res, '订单已过期');
    }

    // 检查支付方式
    if (order.paymentMethod === 'mock') {
      // 模拟支付，直接标记为成功
      await order.markAsPaid({
        wechatTransactionId: `mock_${Date.now()}`,
        notifyData: { mock: true }
      });

      // 更新用户留言余额
      const user = await User.findByPk(userId);
      user.messageBalance += order.messageCount;
      await user.save();

      // 更新留言包购买次数
      const messagePackage = await MessagePackage.findByPk(order.packageId);
      await messagePackage.incrementPurchaseCount();

      // 记录日志
      logger.logPayment(userId, 'mock_payment_success', {
        orderId: order.id,
        orderNo: order.orderNo,
        amount: order.amount
      });

      ResponseHelper.success(res, order, '模拟支付成功');
    } else {
      // 真实支付，这里应该调用微信支付API
      // 暂时返回待实现的响应
      ResponseHelper.success(res, {
        paymentUrl: `mock://payment/${orderNo}`,
        orderNo: order.orderNo
      }, '支付功能待实现');
    }

  } catch (error) {
    logger.error('发起支付失败', { error: error.message, orderNo: req.params.orderNo, userId: req.user?.id });
    ResponseHelper.serverError(res, '发起支付失败', error);
  }
});

// 取消订单
router.post('/orders/:orderNo/cancel', authenticateUser, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await PaymentOrder.findByOrderNo(orderNo);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 检查权限
    if (order.userId !== userId) {
      return ResponseHelper.forbidden(res, '无权操作此订单');
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return ResponseHelper.error(res, '只能取消待支付的订单');
    }

    await order.cancel();

    // 记录日志
    logger.logPayment(userId, 'cancel_order', {
      orderId: order.id,
      orderNo: order.orderNo
    });

    ResponseHelper.success(res, order, '订单取消成功');

  } catch (error) {
    logger.error('取消订单失败', { error: error.message, orderNo: req.params.orderNo, userId: req.user?.id });
    ResponseHelper.serverError(res, '取消订单失败', error);
  }
});

// 支付回调通知（微信支付）
router.post('/notify/wechat', async (req, res) => {
  try {
    // 这里应该验证微信支付回调的签名
    // 暂时返回成功响应
    res.status(200).json({ code: 'SUCCESS', message: '成功' });
  } catch (error) {
    logger.error('处理微信支付回调失败', { error: error.message, body: req.body });
    res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
});

// 查询支付状态
router.get('/orders/:orderNo/status', authenticateUser, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await PaymentOrder.findByOrderNo(orderNo);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 检查权限
    if (order.userId !== userId) {
      return ResponseHelper.forbidden(res, '无权访问此订单');
    }

    ResponseHelper.success(res, {
      orderNo: order.orderNo,
      status: order.status,
      amount: order.amount,
      payTime: order.payTime
    }, '获取支付状态成功');

  } catch (error) {
    logger.error('查询支付状态失败', { error: error.message, orderNo: req.params.orderNo, userId: req.user?.id });
    ResponseHelper.serverError(res, '查询支付状态失败', error);
  }
});

module.exports = router;
