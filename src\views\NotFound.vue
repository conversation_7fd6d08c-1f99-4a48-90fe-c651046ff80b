<template>
  <div class="not-found-page">
    <div class="container mx-auto px-4 py-12 text-center">
      <div class="max-w-md mx-auto">
        <div class="mb-8">
          <i class="iconfont icon-404 text-6xl text-gray-400"></i>
        </div>
        
        <h1 class="text-4xl font-bold text-gray-900 mb-4">404</h1>
        <h2 class="text-xl font-semibold text-gray-700 mb-4">页面未找到</h2>
        <p class="text-gray-600 mb-8">抱歉，您访问的页面不存在或已被移除。</p>
        
        <div class="space-y-4">
          <router-link to="/" class="btn btn-primary">
            返回首页
          </router-link>
          
          <button @click="goBack" class="btn btn-secondary">
            返回上页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'NotFound',
  setup() {
    const router = useRouter()
    
    const goBack = () => {
      router.go(-1)
    }
    
    return {
      goBack
    }
  }
}
</script>
</template>
