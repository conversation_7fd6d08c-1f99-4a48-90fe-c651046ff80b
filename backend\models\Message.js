const { DataTypes, Model } = require('sequelize');

class Message extends Model {
  static getAttributes() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: '用户ID'
      },
      senderName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '留言者名字'
      },
      secretCode: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '暗号'
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '留言内容'
      },
      originalContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '原始内容（过滤前）'
      },
      isPublic: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否公开'
      },
      likeCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '点赞数'
      },
      viewCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '查看次数'
      },
      status: {
        type: DataTypes.ENUM('pending', 'approved', 'rejected', 'hidden'),
        defaultValue: 'approved',
        comment: '审核状态'
      },
      rejectReason: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '拒绝原因'
      },
      hasSensitiveWords: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否包含敏感词'
      },
      sensitiveWords: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '检测到的敏感词列表'
      },
      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: '发布IP地址'
      },
      userAgent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '用户代理'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    };
  }

  static getOptions(sequelize) {
    return {
      sequelize,
      modelName: 'Message',
      tableName: 'messages',
      indexes: [
        {
          fields: ['userId']
        },
        {
          fields: ['senderName']
        },
        {
          fields: ['secretCode']
        },
        {
          fields: ['isPublic']
        },
        {
          fields: ['status']
        },
        {
          fields: ['createdAt']
        },
        {
          fields: ['senderName', 'secretCode']
        }
      ]
    };
  }

  // 静态方法
  static async findPublicMessages(limit = 10, offset = 0) {
    return await this.findAndCountAll({
      where: {
        isPublic: true,
        status: 'approved'
      },
      include: [{
        model: require('./User'),
        as: 'user',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }

  static async findByNameAndCode(senderName, secretCode, limit = 10, offset = 0) {
    return await this.findAndCountAll({
      where: {
        senderName,
        secretCode,
        status: 'approved'
      },
      include: [{
        model: require('./User'),
        as: 'user',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }

  static async findUserMessages(userId, limit = 10, offset = 0) {
    return await this.findAndCountAll({
      where: {
        userId
      },
      include: [{
        model: require('./User'),
        as: 'user',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }

  // 实例方法
  async incrementLike() {
    this.likeCount += 1;
    await this.save();
  }

  async decrementLike() {
    if (this.likeCount > 0) {
      this.likeCount -= 1;
      await this.save();
    }
  }

  async incrementView() {
    this.viewCount += 1;
    await this.save();
  }
}

module.exports = Message;
