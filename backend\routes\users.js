const express = require('express');
const router = express.Router();
const { User, MessageLike } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const { validatePagination } = require('../middleware/validation');
const ResponseHelper = require('../utils/response');
const logger = require('../utils/logger');

// 获取用户点赞的留言列表
router.get('/liked-messages', authenticateUser, validatePagination, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.user.id;

    const result = await MessageLike.getUserLikedMessages(userId, limit, offset);

    ResponseHelper.paginated(res, result.rows, result.count, page, limit, '获取点赞留言成功');

  } catch (error) {
    logger.error('获取用户点赞留言失败', { error: error.message, userId: req.user?.id });
    ResponseHelper.serverError(res, '获取点赞留言失败', error);
  }
});

// 获取用户统计信息
router.get('/stats', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = req.user;

    // 获取用户留言统计
    const messageStats = await require('../config/database').Message.findAndCountAll({
      where: { userId },
      attributes: ['isPublic', 'status']
    });

    // 获取用户点赞统计
    const likeStats = await MessageLike.findAndCountAll({
      where: { userId }
    });

    // 统计数据
    const stats = {
      messageBalance: user.messageBalance,
      totalMessages: messageStats.count,
      publicMessages: messageStats.rows.filter(m => m.isPublic).length,
      privateMessages: messageStats.rows.filter(m => !m.isPublic).length,
      pendingMessages: messageStats.rows.filter(m => m.status === 'pending').length,
      approvedMessages: messageStats.rows.filter(m => m.status === 'approved').length,
      totalLikes: likeStats.count,
      joinDate: user.createdAt,
      lastLogin: user.lastLoginAt
    };

    ResponseHelper.success(res, stats, '获取用户统计成功');

  } catch (error) {
    logger.error('获取用户统计失败', { error: error.message, userId: req.user?.id });
    ResponseHelper.serverError(res, '获取用户统计失败', error);
  }
});

// 上传头像
router.post('/avatar', authenticateUser, async (req, res) => {
  try {
    // 这里应该集成文件上传中间件，暂时返回占位响应
    ResponseHelper.success(res, null, '头像上传功能待实现');
  } catch (error) {
    logger.error('上传头像失败', { error: error.message, userId: req.user?.id });
    ResponseHelper.serverError(res, '上传头像失败', error);
  }
});

module.exports = router;
